<template>
  <div>
    <loading-overlay :loading="loading" />
    <VTour
      name="statistics"
      ref="tour"
      :steps="useVueTour.stepsStatistics"
      :buttonLabels="useVueTour.customButtonLabels"
      highlight
      @onTourEnd="useVueTour.onTourEndRedirect('prepis-hlasov')"
    >
      <template #actions="{ nextStep, prevStep, endTour }">
        <div class="vjt-actions">
          <button type="button" @click.prevent="endAll()">Ukončiť interaktívny návod</button>
          <button type="button" @click.prevent="prevStep">Predchádzajúci krok</button>
          <button type="button" @click.prevent="nextStep">Nasledujúci krok</button>
        </div>
      </template>
    </VTour>
    <div>
      <!-- Enhanced Header Section -->
      <div class="statistics-header">
        <div class="header-controls">
          <div
            class="employee-select-container"
            v-if="
              employees &&
              (currentUser.role === editRole ||
                currentUser.role === viewRole ||
                currentUser.role === technicalRole ||
                myApiUrl === 'https://test-api.vcakarni.sk/api/v1')
            "
          >
            <v-select
              v-model="selectedEmployee"
              :item-title="(employees) => `${employees.first_name} ${employees.last_name}`"
              :items="employees"
              label="Vyber zamestnanca"
              return-object
              class="employee-select"
              @update:modelValue="onSelectChange(selectedEmployee)"
            ></v-select>
          </div>

          <div class="date-picker-container">
            <h3 class="section-title">Zvoľte obdobie</h3>
            <div class="date-picker-wrapper" data-step="1">
              <section v-if="getCurrentRange" class="current-range">
                <span class="range-label">Aktuálne obdobie:</span>
                <span class="range-dates">
                  {{ getCurrentRange.from_date.slice(0, 10).split('-').reverse().join('.') }} -
                  {{ getCurrentRange.to_date.slice(0, 10).split('-').reverse().join('.') }}
                </span>
              </section>
              <DatePicker v-model="dateRange" color="blue" is-range class="date-picker"></DatePicker>
            </div>
          </div>
        </div>
      </div>
      <section data-step="2" style="margin-right: 1em">
        <template v-if="getOverview">
          <!-- <div class="graph-mob" style="margin-bottom: 1em; margin-left: auto; margin-right: auto">
          <div class="graph-mob graph" style="width: 30%;">
            <h3 class="graph-desc">Počet požiadaviek na služby za zvolené obdobie.</h3>
            <div style="padding-right: 1em; height: 100%">
            <Bar :data="dataBar" :options="chartOptionsBar"/>
            </div>
          </div>
          <div class="graph-mob graph" style="width: 30%; margin-left: 1em">
            <h3 class="graph-desc">Počet požiadaviek na konkrétne služby za zvolené obdobie.</h3>
            <Pie :data="dataPie" :options="chartOptionsPie"/>
          </div>
        </div> -->

          <div class="graphs-wrapper">
            <div class="graph-container graph">
              <h3 class="graph-desc">Počet požiadaviek na služby za zvolené obdobie.</h3>
              <div style="padding-right: 1em; height: 100%; user-select: none">
                <Bar :data="dataBar" :options="chartOptionsBar" />
              </div>
            </div>
          </div>

          <div class="graphs-wrapper">
            <div class="graph-container graph">
              <h3 class="graph-desc">Počet nájdených požiadaviek pomocou zoznamu čakateľov za zvolené obdobie.</h3>
              <div style="padding-right: 1em; height: 100%; user-select: none">
                <Bar :data="dataWaitingList" :options="chartOptionsBar" />
              </div>
            </div>
          </div>

          <div class="graph-container graph-pie">
            <h3 class="graph-desc">Počet požiadaviek na konkrétne služby za zvolené obdobie.</h3>
            <div class="button-filters-wrapper">
              <button
                :class="{
                  active: selectedFilters.includes('ALL'),
                  'non-active': !selectedFilters.includes('ALL')
                }"
                @click="toggleFilter('ALL')"
              >
                Všetky
              </button>
              <button
                :class="{
                  active: selectedFilters.includes('A'),
                  'non-active': !selectedFilters.includes('A')
                }"
                @click="toggleFilter('A')"
              >
                Presný čas - lekár
              </button>
              <button
                :class="{
                  active: selectedFilters.includes('D'),
                  'non-active': !selectedFilters.includes('D')
                }"
                @click="toggleFilter('D')"
              >
                Presný čas - pacient
              </button>
              <button
                :class="{
                  active: selectedFilters.includes('B'),
                  'non-active': !selectedFilters.includes('B')
                }"
                @click="toggleFilter('B')"
              >
                Orientačný čas - lekár
              </button>
              <button
                :class="{
                  active: selectedFilters.includes('E'),
                  'non-active': !selectedFilters.includes('E')
                }"
                @click="toggleFilter('E')"
              >
                Orientačný čas - pacient
              </button>
            </div>
            <div class="graph-charts">
              <div class="graph-chart" style="user-select: none">
                <Doughnut ref="piechart" :data="dataPie" :options="chartOptionsPie" :plugins="[CenterTextPlugin]" />
              </div>
            </div>
          </div>

          <div class="graph-wrapper">
            <div class="flex-center year">
              <div style="display: flex; justify-content: center">
                <h3 class="mr-2">Zvoľte rok:</h3>
                <select v-model="thisYear" class="select-container">
                  <option v-for="year in availableYears" :key="year" :value="year">
                    {{ year }}
                  </option>
                </select>
              </div>
              <h3 class="graph-desc">Celkový prehľad počtu požiadaviek na služby za kalendárny rok.</h3>
            </div>

            <div class="graph graph-mob" style="padding-right: 1em; user-select: none">
              <Line :data="dataLine" :options="chartOptionsLine" />
            </div>
          </div>
        </template>
      </section>
    </div>
  </div>
</template>

<script setup>
  import { DatePicker } from 'v-calendar';
  import 'v-calendar/dist/style.css';
  import { computed, onMounted, reactive, ref, watch } from 'vue';
  import { Bar, Doughnut, Line } from 'vue-chartjs';
  import { ArcElement, BarElement, CategoryScale, Chart as ChartJS, Colors, Legend, LinearScale, LineElement, PointElement, Title, Tooltip } from 'chart.js';
  import ChartDataLabels from 'chartjs-plugin-datalabels';
  import { useOverview } from '../../stores/statistics.js';
  import '../../assets/css/responsive-design.css';
  import { useEmployee } from '../../stores/employee.js';
  import { useUser } from '../../stores/user.js';
  import { vueTour } from '../../stores/vue-tour.js';
  import { handleToastError, handleToastSuccess } from '../../utility/notification.js';

  ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, ArcElement, PointElement, LineElement, Colors, ChartDataLabels);

  const useOverviewStore = useOverview();
  const useEmployeeStore = useEmployee();
  const useUserStore = useUser();
  let from, to;
  const loading = ref(true);
  const thisYear = ref(new Date().getFullYear());
  const availableYears = [thisYear.value + 1, thisYear.value, thisYear.value - 1, thisYear.value - 2];
  const activeButton = ref('C');
  const tour = ref(null);
  const useVueTour = vueTour();
  const endAll = async () => {
    const keys = [
      'vjt-notice',
      'vjt-vacation',
      'vjt-directory',
      'vjt-service',
      'vjt-aboutus',
      'vjt-openHour',
      'vjt-lunchHour',
      'vjt-calendar',
      'vjt-signature',
      'vjt-information',
      'vjt-statistics',
      'vjt-medicine',
      'vjt-voice',
      'vjt-image',
      'vjt-reservation'
    ];
    await keys.forEach(function (key) {
      localStorage.setItem(key, 'true');
    });
    window.location.reload();
  };
  onMounted(async () => {
    loading.value = true;

    if (!useEmployeeStore.getEmployee.length) {
      // await useEmployeeStore.fetchEmployee()
    }
    if (changedUser.value) {
      selectedEmployee.value = changedUser.value;
    } else if (currentUser.value) {
      if (useUserStore.findActiveUser(currentUser.value)) {
        selectedEmployee.value = currentUser.value;
      } else {
        selectedEmployee.value = employees.value[0];
      }
    }
    const success = await useOverviewStore.fetchOverview(from, to, selectedEmployee.value.id_user);
    await useOverviewStore.fetchWaitingListOverall(from, to, selectedEmployee.value.id_user);
    if (success && success.data && success.data.data) {
      originalData = JSON.parse(JSON.stringify(success.data.data));
    }
    activeButton.value = 'C';
    selectedFilters.value = ['ALL'];
    await useOverviewStore.fetchOverviewYear(thisYear.value, selectedEmployee.value.id_user);

    if (localStorage.getItem('showTour') === 'true') {
      await useVueTour.startTour(tour);
    }
    redrawChart();
    loading.value = false;
  });
  const viewRole = 'System operator';
  const editRole = 'Head of office';
  const technicalRole = 'Technical employee';
  const myApiUrl = import.meta.env.VITE_API_URL;
  const selectedEmployee = ref();
  const employees = computed(() => useEmployeeStore.getEmployee);
  const currentUser = computed(() => {
    if (useEmployeeStore.currentlyLoggedUser[0]) {
      return useEmployeeStore.currentlyLoggedUser[0];
    } else return [];
  });
  const onSelectChange = async (myValue) => {
    loading.value = true;
    if (myValue) {
      let start, end;
      start = dateRange.value.start.toISOString().slice(0, 10).split('-').join('-');
      end = dateRange.value.end.toISOString().slice(0, 10).split('-').join('-');
      const success = await useOverviewStore.fetchOverview(start, end, myValue.id_user);
      await useOverviewStore.fetchWaitingListOverall(start, end, myValue.id_user);
      if (success && success.data && success.data.data) {
        originalData = JSON.parse(JSON.stringify(success.data.data));
        activeButton.value = 'C';
      }
      selectedFilters.value = ['ALL'];
      // await useOverviewStore.fetchFreeByDate(start, end,myValue.id_user)
      await useOverviewStore.fetchOverviewYear(thisYear.value, myValue.id_user);
      await useUserStore.setCurrentUser(myValue);
    }
    redrawChart();
    loading.value = false;
  };
  const changedUser = computed(() => {
    if (useUserStore.changedUser[0]) {
      return useUserStore.changedUser[0];
    } else return null;
  });
  const overview = reactive([]);
  const date = ref(new Date());

  const dateRange = ref({
    start: new Date(),
    end: date.value
  });

  watch(
    () => useOverviewStore.getOverview[0],
    (newOverview) => {
      if (newOverview) {
        overview.value = newOverview;
      }
    }
  );

  watch(dateRange, () => {
    getNewDates();
  });

  watch(thisYear, () => {
    useOverviewStore.fetchOverviewYear(thisYear.value, selectedEmployee.value.id_user);
  });

  const getNewDates = () => {
    parseDates(dateRange.value);
  };

  const parseDates = async (myDates) => {
    loading.value = true;
    let start, end;
    start = myDates.start.toISOString().slice(0, 10).split('-').join('-');
    end = myDates.end.toISOString().slice(0, 10).split('-').join('-');
    const success = await useOverviewStore.fetchOverview(start, end, selectedEmployee.value.id_user);
    await useOverviewStore.fetchWaitingListOverall(start, end, selectedEmployee.value.id_user);
    if (success && success.data && success.data.data) {
      originalData = JSON.parse(JSON.stringify(success.data.data));
      activeButton.value = 'C';
    }
    selectedFilters.value = ['ALL'];
    //  await useOverviewStore.fetchPremiumByDate(start, end,selectedEmployee.value.id_user)
    // await useOverviewStore.fetchFreeByDate(start, end,selectedEmployee.value.id_user)
    redrawChart();
    loading.value = false;
  };

  const getWaitingList = computed(() => {
    return useOverviewStore.getWaitingListOverall[0];
  });

  const getOverview = computed(() => {
    return useOverviewStore.getOverview[0];
  });

  const getOverviewYear = computed(() => {
    return useOverviewStore.getOverviewYear[0];
  });

  const getCurrentRange = computed(() => {
    return useOverviewStore.range[0];
  });

  const getFree = computed(() => {
    if (getOverview.value) {
      return getOverview.value.total.free;
    } else return [];
  });

  const getClaimedWaitingList = computed(() => {
    if (getWaitingList.value) {
      return getWaitingList.value.claim;
    } else return [];
  });

  const getExpiredWaitingList = computed(() => {
    if (getWaitingList.value) {
      return getWaitingList.value.expiration;
    } else return [];
  });

  const getPaid = computed(() => {
    if (getOverview.value) {
      return getOverview.value.total.premium;
    } else return [];
  });

  const getEmergency = computed(() => {
    if (getOverview.value) {
      return getOverview.value.total.emergency.count_created_by_user;
    } else return [];
  });

  const getDelayAveragesFree = computed(() => {
    if (getDelayAverages.value) {
      return getDelayAverages.value.free;
    } else return [];
  });

  const getDelayAveragesPaid = computed(() => {
    if (getDelayAverages.value) {
      return getDelayAverages.value.premium;
    } else return [];
  });

  const getMonths = computed(() => {
    if (getOverviewYear.value) {
      const monthNames = ['january', 'february', 'march', 'april', 'may', 'june', 'july', 'august', 'september', 'october', 'november', 'december'];

      const monthsDataFree = monthNames.map((month) => getOverviewYear.value.months[month].free);
      const monthsDataPremium = monthNames.map((month) => getOverviewYear.value.months[month].premium);
      const monthsDataEmergency = monthNames.map((month) => getOverviewYear.value.months[month].emergency);

      return { monthsDataFree, monthsDataPremium, monthsDataEmergency };
    } else {
      return [];
    }
  });

  const getServicesValues = computed(() => {
    const services = reactive([]);
    if (getOverview.value) {
      for (let i = 0; i < getOverview.value.by_service.length; i++) {
        if (getOverview.value) {
          services.push(parseInt(getOverview.value.by_service[i].count_reservation.count_created_by_client));
          services.push(parseInt(getOverview.value.by_service[i].count_reservation.count_created_by_user));
        }
      }
      return services;
    } else return [];
  });

  const getServicesNames = computed(() => {
    const services = reactive([]);
    if (getOverview.value) {
      for (let i = 0; i < getOverview.value.by_service.length; i++) {
        if (getOverview.value) {
          services.push(getOverview.value.by_service[i].name + ' - pacient');
          services.push(getOverview.value.by_service[i].name + ' - lekár');
        }
      }
      return services;
    } else return [];
  });

  const getServiceColor = computed(() => {
    const services = reactive([]);

    if (getOverview.value) {
      for (let i = 0; i < getOverview.value.by_service.length; i++) {
        if (getOverview.value) {
          services.push(getOverview.value.by_service[i].calendar_color);
          services.push(getOverview.value.by_service[i].calendar_color);
        }
      }
      return services;
    } else return [];
  });

  function hexToRgba(hex, alpha) {
    if (!hex) {
      return 'rgba(0,0,0,0.5)';
    }
    hex = hex.replace('#', '');
    const r = parseInt(hex.substring(0, 2), 16);
    const g = parseInt(hex.substring(2, 4), 16);
    const b = parseInt(hex.substring(4, 6), 16);
    alpha = Math.min(1, Math.max(0, alpha));
    return `rgba(${r},${g},${b},${alpha})`;
  }
  const piechart = ref(null);
  const dataPie = computed(() => {
    if (getServicesValues.value.length === 0) {
      return {
        labels: ['Žiadne dáta'],
        datasets: [
          {
            data: [1],
            backgroundColor: hexToRgba('#07327a', 0.5)
          }
        ]
      };
    } else
      return {
        labels: getServicesNames.value,
        datasets: [
          {
            backgroundColor: getServiceColor.value.map((hex) => hexToRgba(hex, 0.5)),
            data: getServicesValues.value
          }
        ]
      };
  });

  /*
datasets: [
      {
        label: 'Dataset 1',
        backgroundColor: 'rgba(255, 99, 132, 0.5)',
        data: [10, 20, 30, 40, 50]
      },
      {
        label: 'Dataset 2',
        backgroundColor: 'rgba(54, 162, 235, 0.5)',
        data: [15, 25, 35, 45, 55]
      },
      {
        label: 'Dataset 3',
        backgroundColor: 'rgba(75, 192, 192, 0.5)',
        data: [20, 30, 40, 50, 60]
      }
    ]
 */

  const dataBar = computed(() => {
    return {
      labels: ['Orientačný čas', 'Presný čas', 'Akútny stav'],
      datasets: [
        {
          label: ['Orientačný čas - vytvorené pacientom'],
          backgroundColor: 'rgba(255, 99, 132, 0.5)',
          data: [getFree.value.count_created_by_client, 0, 0]
        },
        {
          label: ['Orientačný čas - vytvorené lekárom'],
          backgroundColor: 'rgba(54,235,78,0.5)',
          data: [getFree.value.count_created_by_user, 0, 0]
        },
        {
          label: ['Presný čas - vytvorené pacientom'],
          backgroundColor: 'rgba(54, 162, 235, 0.5)',
          data: [0, getPaid.value.count_created_by_client, 0]
        },
        {
          label: ['Presný čas - vytvorené lekárom'],
          backgroundColor: 'rgba(154,54,235,0.5)',
          data: [0, getPaid.value.count_created_by_user, 0]
        },
        {
          label: ['Akútny stav'],
          backgroundColor: 'rgba(255, 205, 86, 0.5)',
          data: [0, 0, getEmergency.value]
        }
      ]
    };
  });
  const dataWaitingList = computed(() => {
    if (getWaitingList.value) {
      return {
        labels: ['Potvrdené požiadavky', 'Nepotvrdené požiadavky'],
        datasets: [
          {
            label: ['Potvrdené požiadavky'],
            backgroundColor: 'rgba(154,54,235,0.5)',
            data: [getClaimedWaitingList.value.total_claimed]
          },
          {
            label: ['Nepotvrdené požiadavky'],
            backgroundColor: 'rgba(255, 205, 86, 0.5)',
            data: [getExpiredWaitingList.value.total_expired]
          }
        ]
      };
    } else
      return {
        labels: [],
        datasets: [
          {
            data: []
          }
        ]
      };
  });

  const chartOptionsBar = computed(() => {
    return {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: true
        },
        datalabels: {
          color: '#000000',
          display: function (context) {
            return context.dataset.data[context.dataIndex] !== 0;
          }
        }
      },
      scales: {
        y: {
          stacked: true,
          beginAtZero: true
        },
        x: {
          stacked: true
        }
      },
      ticks: {
        precision: 0
      }
    };
  });
  const dataLine = computed(() => {
    return {
      labels: ['Január', 'Február', 'Marec', 'Apríl', 'Máj', 'Jún', 'Júl', 'August', 'September', 'Október', 'November', 'December'],
      datasets: [
        {
          label: 'Orientačný čas',
          backgroundColor: '#41B883',
          data: getMonths.value.monthsDataFree
        },
        {
          label: 'Presný čas',
          backgroundColor: '#DD1B16',
          data: getMonths.value.monthsDataPremium
        },
        {
          label: 'Akútny stav',
          backgroundColor: '#00D8FF',
          data: getMonths.value.monthsDataEmergency
        }
      ]
    };
  });

  function redrawChart() {
    if (piechart.value && piechart.value.chart) {
      piechart.value.chart.update();
    }
  }

  const CenterTextPlugin = {
    id: 'centerText',
    beforeDraw(chart) {
      const {
        ctx,
        chartArea: { left, right, top, bottom }
      } = chart;

      if (!chart.data.datasets.length) return;

      const datasets = chart.data.datasets[0].data;
      const total = datasets.reduce((sum, value) => sum + value, 0);

      const xCenter = (left + right) / 2;
      const yCenter = (top + bottom) / 2;

      ctx.save();
      ctx.font = 'bold 20px Arial'; // Adjust font style and size
      ctx.fillStyle = '#07327a'; // Adjust color
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText(total, xCenter, yCenter); // Draw text in the actual center
      ctx.restore();
    }
  };

  const chartOptionsPie = computed(() => {
    return {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        datalabels: {
          color: '#000000',
          formatter: (value, context) => {
            const dataPoints = context.chart.data.datasets[0].data;
            if (value === 0) {
              return '';
            }

            function totalSum(total, dataPoint) {
              return total + dataPoint;
            }

            const totalValue = dataPoints.reduce(totalSum, 0);
            const percentageValue = ((value / totalValue) * 100).toFixed(0);
            return [`${percentageValue}%` + ' ' + '(' + value + ')'];
          }
        },
        legend: {
          display: true,
          position: 'top',
          labels: {
            padding: 18
          }
        }
      }
    };
  });

  const chartOptionsLine = computed(() => {
    return {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: 'top'
        },
        datalabels: {
          display: true,
          align: 'right',
          anchor: 'right',
          formatter: (value, context) => {
            return value === 0 ? '' : value;
          }
        }
      },
      scales: {
        y: {
          beginAtZero: true
        }
      },
      ticks: {
        precision: 0
      }
    };
  });
  let originalData = [];
  const selectedFilters = ref([]);
  const toggleFilter = (filter) => {
    if (filter === 'ALL') {
      selectedFilters.value = ['ALL'];
    } else {
      selectedFilters.value = [filter];
    }
    filterData();
  };

  const filterData = () => {
    useOverviewStore.getOverview[0].by_service = originalData.by_service
      .filter((service) => {
        // Determine if the service matches the selected filter
        if (selectedFilters.value.includes('ALL')) {
          return true; // Show all services when "Všetky" is selected
        }

        // Check conditions for each filter
        if (selectedFilters.value.includes('A') && service.premium) {
          return true; // "Presný čas - lekár" (Premium)
        }
        if (selectedFilters.value.includes('D') && service.premium) {
          return true; // "Presný čas - pacient" (Premium)
        }
        if (selectedFilters.value.includes('B') && !service.premium) {
          return true; // "Orientačný čas - lekár" (Non-premium)
        }
        if (selectedFilters.value.includes('E') && !service.premium) {
          return true; // "Orientačný čas - pacient" (Non-premium)
        }

        return false; // Filter out services that don't match
      })
      .map((service) => {
        const newService = JSON.parse(JSON.stringify(service));

        // Modify counts for remaining services based on the selected filter
        if (selectedFilters.value.includes('A') && service.premium) {
          // Set client count to 0 for "Presný čas - lekár" (Premium)
          newService.count_reservation.count_created_by_client = 0;
        }
        if (selectedFilters.value.includes('D') && service.premium) {
          // Set user count to 0 for "Presný čas - pacient" (Premium)
          newService.count_reservation.count_created_by_user = 0;
        }
        if (selectedFilters.value.includes('B') && !service.premium) {
          // Set client count to 0 for "Orientačný čas - lekár" (Non-premium)
          newService.count_reservation.count_created_by_client = 0;
        }
        if (selectedFilters.value.includes('E') && !service.premium) {
          // Set user count to 0 for "Orientačný čas - pacient" (Non-premium)
          newService.count_reservation.count_created_by_user = 0;
        }

        return newService;
      });

    if (piechart.value) {
      if (useOverviewStore.getOverview[0].by_service.length === 0) {
        const noDataObj = {
          calendar_color: '#07327a',
          name: 'Žiadne dáta',
          count_reservation: {
            count_created_by_client: 0,
            count_created_by_user: 1
          }
        };
        useOverviewStore.getOverview[0].by_service.push(noDataObj);
      }
      piechart.value.chart.update();
    }
  };
</script>

<style scoped>
  h1,
  h2 {
    text-align: center;
    line-height: 1.3;
  }

  .flex-center {
    display: flex;
    justify-content: center;
  }
  .employee-select {
    min-width: 250px;
    max-width: 400px;
  }
  .select-area-statistics {
    border: none;
    position: relative;
    display: flex;
    justify-content: space-between;
  }
  .year {
    flex-direction: column;
    align-content: center;
  }

  label {
    font-weight: 600;
    display: flex;
    align-items: center;
  }

  .select-container {
    padding: 0.2em 0.75em;
    border-radius: 3px;
    background: rgba(202, 198, 195, 0.575);
  }

  .select-container:hover {
    background: rgba(202, 198, 195, 0.764);
  }

  .graphs-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    gap: 5em;
    margin-right: 1em;
  }

  .graph-desc {
    margin: 0.75em 0;
    text-align: center;
  }

  .graph {
    margin-bottom: 6em;
    margin-left: auto;
    margin-right: auto;
    text-align: center;
    width: 90%;
    height: 350px;
  }

  .graph-pie {
    margin-bottom: 3em;
    margin-left: auto;
    margin-right: auto;
    text-align: center;
    width: 90%;
  }

  .graph-charts {
    display: flex;
    flex-wrap: wrap;
    gap: 1em;
    justify-content: center;
  }

  .graph-chart {
    flex: 1;
    width: calc(50% - 1em);
    height: 450px;
  }

  @media screen and (max-width: 673px) {
    .employee-select {
      margin-right: 1em;
    }

    .select-area-statistics {
      display: block;
    }
  }
  @media (max-width: 940px) {
    .graph-charts {
      flex-direction: column;
    }

    .graph-chart {
      width: 100%;
    }
  }

  .active {
    margin-right: 0.5em;
    padding: 3px 8px 3px 8px;
    margin-bottom: 10px;
    border-radius: 5px;
    width: 150px;
    min-height: 50px;
    text-align: center;
    cursor: pointer;
    font-size: 14px;
    line-height: 15px;
    text-transform: uppercase;
    background-color: white;
    border: 3px solid var(--blue);
    color: var(--blue);
  }

  .non-active {
    margin-right: 0.5em;
    background-color: var(--blue);
    padding: 6px 8px 6px 8px;
    margin-bottom: 10px;
    border-radius: 5px;
    width: 150px;
    min-height: 50px;
    text-align: center;
    cursor: pointer;
    font-size: 14px;
    line-height: 15px;
    text-transform: uppercase;
    color: white;
  }
  .non-active:hover {
    cursor: pointer;
    opacity: 0.7;
  }
  .button-filters-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  @media screen and (max-width: 870px) {
    .graph {
      flex-direction: column;
      width: 100%;
    }

    .graph-mob {
      flex-direction: column;
      align-items: center;
      width: 90% !important;
      margin-left: 0.5em;
    }
    .button-filters-wrapper {
      flex-wrap: wrap;
      justify-content: center;
    }
    .non-active {
      font-size: 12px;
      height: 60px;
    }
    .active {
      font-size: 12px;
      height: 60px;
    }
  }

  @media screen and (max-width: 400px) {
    .graph-desc {
      font-size: 1em;
      margin-inline: 1em;
      margin-right: 1em;
    }

    .graph-chart {
      flex: none;
    }
  }

  /* Enhanced Design Styles */
  .statistics-header {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 12px;
    padding: 2em;
    margin-bottom: 2em;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border: 1px solid #e2e8f0;
  }

  .header-controls {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 2em;
    align-items: start;
  }

  .employee-select-container {
    display: flex;
    flex-direction: column;
    gap: 0.75em;
  }

  .employee-select {
    max-width: 300px;
  }

  .date-picker-container {
    display: flex;
    flex-direction: column;
    gap: 1em;
  }

  .section-title {
    color: #1e293b;
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5em;
  }

  .section-icon {
    color: var(--green);
  }

  .date-picker-wrapper {
    display: flex;
    flex-direction: column;
    gap: 1em;
  }

  .current-range {
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-radius: 8px;
    padding: 1em;
    display: flex;
    flex-direction: column;
    gap: 0.5em;
  }

  .range-label {
    font-weight: 600;
    color: #1e293b;
    font-size: 0.9rem;
  }

  .range-dates {
    font-weight: 600;
    color: var(--blue);
  }

  .date-picker {
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  /* Responsive Design for Enhanced Header */
  @media (max-width: 768px) {
    .statistics-header {
      padding: 1.5em;
    }

    .header-controls {
      grid-template-columns: 1fr;
      gap: 1.5em;
    }

    .employee-select {
      max-width: 100%;
    }
  }

  @media (max-width: 480px) {
    .statistics-header {
      padding: 1em;
      margin-bottom: 1.5em;
    }

    .current-range {
      padding: 0.75em;
    }

    .section-title {
      font-size: 1.1rem;
    }
  }
</style>
