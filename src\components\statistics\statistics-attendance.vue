<template>
  <div>
    <loading-overlay :loading="loading" />
    <div>
      <!-- Enhanced Header Section -->
      <div class="statistics-header">
        <div class="header-controls">
          <div
            class="employee-select-container"
            v-if="
              employees &&
              (currentUser.role === editRole ||
                currentUser.role === viewRole ||
                currentUser.role === technicalRole ||
                myApiUrl === 'https://test-api.vcakarni.sk/api/v1')
            "
          >
            <v-select
              v-model="selectedEmployee"
              :item-title="(employees) => `${employees.first_name} ${employees.last_name}`"
              :items="employees"
              label="Vyber zamestnanca"
              return-object
              class="employee-select"
              @update:modelValue="onSelectChange(selectedEmployee)"
            ></v-select>
          </div>

          <div class="date-picker-container">
            <h3 class="section-title">Zvoľte obdobie</h3>
            <div class="date-picker-wrapper">
              <section v-if="getCurrentRange" class="current-range">
                <span class="range-label">Aktu<PERSON>lne obdobie:</span>
                <span class="range-dates">
                  {{ getCurrentRange.from_date.slice(0, 10).split('-').reverse().join('.') }} -
                  {{ getCurrentRange.to_date.slice(0, 10).split('-').reverse().join('.') }}
                </span>
              </section>
              <DatePicker v-model="dateRange" color="blue" is-range class="date-picker"></DatePicker>
            </div>
          </div>
        </div>
      </div>

      <section style="margin-right: 1em">
        <template v-if="getOverview">
          <div class="graphs-wrapper">
            <div class="graph-container graph-pie graph-pie-total">
              <h1>Celková štatistika</h1>
              <h3 class="graph-desc">Celkový prehľad dochádzky na služby za zvolené obdobie.</h3>
              <div style="padding-right: 1em; height: 100%; user-select: none">
                <Pie :data="dataBarTotalStats" :options="createPieTotal" />
              </div>
            </div>

            <div
              class="graph-container graph-pie"
              v-if="
                officeInfo.office_type === 'Doctor' ||
                myApiUrl === 'https://test-api.vcakarni.sk/api/v1' ||
                myApiUrl === 'https://demo-api.vcakarni.sk/api/v1' ||
                myApiUrl === 'http://localhost/api/v1'
              "
            >
              <h1>Orientačný čas</h1>
              <h3 class="graph-desc">Prehľad účasti klientov na služby v orientačných časoch za zvolené obdobie.</h3>
              <div class="graph-charts">
                <div class="graph-chart" style="user-select: none">
                  <Pie :data="dataBarFreeAttendance" :options="createPieAttendance" />
                </div>
                <div class="graph-chart graph-smaller-legend" style="user-select: none">
                  <Pie :data="dataPieFreeDeleted" :options="createPieAttendance" />
                </div>
              </div>
            </div>

            <div class="graph-container graph-pie">
              <h1>Presný čas</h1>
              <h3 class="graph-desc">Prehľad účasti klientov na služby v presných časoch za zvolené obdobie.</h3>
              <div class="graph-charts">
                <div class="graph-chart" style="user-select: none">
                  <Pie :data="dataBarPremiumAttendance" :options="createPieAttendance" />
                </div>
                <div class="graph-chart graph-smaller-legend" style="user-select: none">
                  <Pie :data="dataPiePremiumDeleted" :options="createPieAttendance" />
                </div>
              </div>
            </div>

            <div class="graph-container graph">
              <h1>Priemerne a medianove oneskorenie</h1>
              <h3 class="graph-desc">Priemerné a najčastejšie sa vyskytujúce oneskorenie na služby za zvolené obdobie.</h3>
              <div style="padding-right: 1em; height: 100%; user-select: none">
                <Bar :data="dataBarDelayAverage" :options="createChartOptionsDelay" />
              </div>
            </div>

            <div class="tables">
              <div class="table-wrapper">
                <h1 class="table-header">Klienti s najväčším počtom zmeškaných účasti</h1>
                <h3 class="graph-desc">Klienti s najväčším počtom zmeškaných/zrušených účastí.</h3>
                <div class="table-cointainer">
                  <v-table density="compact">
                    <thead>
                      <tr>
                        <th>Klient</th>
                        <th>Počet</th>
                      </tr>
                    </thead>

                    <tbody>
                      <tr v-for="client in getTopNoShowupsTotal" :key="client">
                        <td>{{ client.client_name }}</td>
                        <td>{{ client.no_show_count }}</td>
                      </tr>
                    </tbody>
                  </v-table>
                </div>
              </div>
              <div class="table-wrapper">
                <h1 class="table-header">Klienti s najväčším počtom bez meškania na vyšetrenie</h1>
                <h3 class="graph-desc">Klienti s najvyšším počtom včasných príchodov na služby.</h3>
                <div class="table-cointainer" style="margin-bottom: 1.5em">
                  <v-table density="compact">
                    <thead>
                      <tr>
                        <th>Klient</th>
                        <th>Počet</th>
                      </tr>
                    </thead>

                    <tbody>
                      <tr v-for="client in getTopNoDelayTotal" :key="client">
                        <td>{{ client.client_name }}</td>
                        <td>{{ client.arrived_no_delay_count }}</td>
                      </tr>
                    </tbody>
                  </v-table>
                </div>
              </div>
            </div>
          </div>
        </template>
      </section>
    </div>
  </div>
</template>

<script setup>
  import { DatePicker } from 'v-calendar';
  import 'v-calendar/dist/style.css';
  import { computed, onMounted, reactive, ref, watch } from 'vue';
  import { Bar, Line, Pie } from 'vue-chartjs';

  import { ArcElement, BarElement, CategoryScale, Chart as ChartJS, Colors, Legend, LinearScale, LineElement, PointElement, Title, Tooltip } from 'chart.js';
  import ChartDataLabels from 'chartjs-plugin-datalabels';
  import { useOverview } from '../../stores/statistics.js';
  import '../../assets/css/responsive-design.css';
  import { useEmployee } from '../../stores/employee.js';
  import { useUser } from '../../stores/user.js';
  import { handleToastError, handleToastSuccess } from '../../utility/notification.js';

  const useEmployeeStore = useEmployee();
  const useUserStore = useUser();
  const useOfficeStore = useOffice();
  import { useAuthentication } from '../../stores/authentication.js';
  import { useOffice } from '../../stores/office.js';
  const useAuthenticationStore = useAuthentication();
  ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, ArcElement, PointElement, LineElement, Colors, ChartDataLabels);

  const useOverviewStore = useOverview();
  let from, to;
  const loading = ref(true);

  onMounted(async () => {
    loading.value = true;
    if (!useEmployeeStore.getEmployee.length) {
      //await useEmployeeStore.fetchEmployee()
    }
    if (changedUser.value) {
      selectedEmployee.value = changedUser.value;
    } else if (currentUser.value) {
      if (useUserStore.findActiveUser(currentUser.value)) {
        selectedEmployee.value = currentUser.value;
      } else {
        selectedEmployee.value = employees.value[0];
      }
    }
    await useOverviewStore.fetchOverview(from, to, selectedEmployee.value.id_user);
    await useOverviewStore.fetchPremiumByDate(from, to, selectedEmployee.value.id_user);
    await useOverviewStore.fetchFreeByDate(from, to, selectedEmployee.value.id_user);
    await useOverviewStore.fetchTopNoShowups(from, to, selectedEmployee.value.id_user);
    await useOverviewStore.fetchTopNoDelay(from, to, selectedEmployee.value.id_user);
    await useOverviewStore.fetchDelayAverages(from, to, selectedEmployee.value.id_user);
    await useOverviewStore.fetchTotalStats(from, to, selectedEmployee.value.id_user);
    loading.value = false;
  });

  const office = computed(() => {
    return useEmployeeStore.myOffice;
  });
  const viewRole = 'System operator';
  const myApiUrl = import.meta.env.VITE_API_URL;
  const editRole = 'Head of office';
  const technicalRole = 'Technical employee';
  const selectedEmployee = ref();
  const employees = computed(() => useEmployeeStore.getEmployee);
  const officeInfo = computed(() => useOfficeStore.getMyOffice[0]);
  const currentUser = computed(() => {
    if (useEmployeeStore.currentlyLoggedUser[0]) {
      return useEmployeeStore.currentlyLoggedUser[0];
    } else return [];
  });
  const onSelectChange = async (myValue) => {
    loading.value = true;
    if (myValue) {
      let start, end;
      start = dateRange.value.start.toISOString().slice(0, 10).split('-').join('-');
      end = dateRange.value.end.toISOString().slice(0, 10).split('-').join('-');
      await useOverviewStore.fetchOverview(start, end, myValue.id_user);
      await useOverviewStore.fetchPremiumByDate(start, end, myValue.id_user);
      await useOverviewStore.fetchFreeByDate(start, end, myValue.id_user);
      await useOverviewStore.fetchTopNoShowups(start, end, myValue.id_user);
      await useOverviewStore.fetchTopNoDelay(start, end, myValue.id_user);
      await useOverviewStore.fetchDelayAverages(start, end, myValue.id_user);
      await useOverviewStore.fetchTotalStats(start, end, myValue.id_user);
      await useUserStore.setCurrentUser(myValue);
    }
    loading.value = false;
  };
  const changedUser = computed(() => {
    if (useUserStore.changedUser[0]) {
      return useUserStore.changedUser[0];
    } else return null;
  });
  const overview = reactive([]);

  const date = ref(new Date());

  const dateRange = ref({
    start: new Date(),
    end: date.value
  });

  watch(
    () => useOverviewStore.getOverview[0],
    (newOverview) => {
      if (newOverview) {
        overview.value = newOverview;
      }
    }
  );

  watch(dateRange, () => {
    getNewDates();
  });

  const getNewDates = () => {
    parseDates(dateRange.value);
  };

  const parseDates = async (myDates) => {
    loading.value = true;
    let start, end;
    start = myDates.start.toISOString().slice(0, 10).split('-').join('-');
    end = myDates.end.toISOString().slice(0, 10).split('-').join('-');
    await useOverviewStore.fetchOverview(start, end, selectedEmployee.value.id_user);
    await useOverviewStore.fetchPremiumByDate(start, end, selectedEmployee.value.id_user);
    await useOverviewStore.fetchFreeByDate(start, end, selectedEmployee.value.id_user);
    await useOverviewStore.fetchTopNoShowups(start, end, selectedEmployee.value.id_user);
    await useOverviewStore.fetchTopNoDelay(start, end, selectedEmployee.value.id_user);
    await useOverviewStore.fetchDelayAverages(start, end, selectedEmployee.value.id_user);
    await useOverviewStore.fetchTotalStats(start, end, selectedEmployee.value.id_user);
    loading.value = false;
  };

  const getOverview = computed(() => {
    return useOverviewStore.getOverview[0];
  });

  const getPremiumByDate = computed(() => {
    return useOverviewStore.getPremiumByDate[0];
  });

  const getFreeByDate = computed(() => {
    return useOverviewStore.getFreeByDate[0];
  });

  const getTotalStats = computed(() => {
    return useOverviewStore.getTotalStats[0];
  });

  const getTopNoShowups = computed(() => {
    return useOverviewStore.getTopNoShowups[0];
  });

  const getTopNoDelay = computed(() => {
    return useOverviewStore.getTopNoDelay[0];
  });

  const getDelayAverages = computed(() => {
    return useOverviewStore.getDelayAverages[0];
  });

  const getCurrentRange = computed(() => {
    return useOverviewStore.range[0];
  });

  const getDelayAveragesFree = computed(() => {
    if (getDelayAverages.value) {
      return getDelayAverages.value.free;
    } else return [];
  });

  const getDelayAveragesPaid = computed(() => {
    if (getDelayAverages.value) {
      return getDelayAverages.value.premium;
    } else return [];
  });

  const getTopNoShowupsTotal = computed(() => {
    if (getTopNoShowups.value) {
      let top10Users = getTopNoShowups.value.sort((a, b) => b.no_show_count - a.no_show_count);
      let top10UsersTotal = top10Users.slice(0, 10);
      return top10UsersTotal;
    } else return [];
  });

  const getTopNoDelayTotal = computed(() => {
    if (getTopNoDelay.value) {
      let top10UsersNoDelay = getTopNoDelay.value.sort((a, b) => b.arrived_no_delay_count - a.arrived_no_delay_count);
      let top10UsersNoDelayTotal = top10UsersNoDelay.slice(0, 10);
      return top10UsersNoDelayTotal;
    } else return [];
  });

  const createChartOptionsDelay = computed(() => {
    return chartOptionsBar('minút');
  });

  const createChartOptionsBar = computed(() => {
    return chartOptionsBar('počet');
  });

  const createPieTotal = computed(() => {
    return chartOptionsPie('top');
  });

  const createPieAttendance = computed(() => {
    if (window.innerWidth < 673) {
      return chartOptionsPie('top');
    } else {
      return chartOptionsPie('right');
    }
  });
  function hexToRgba(hex, alpha) {
    if (!hex) {
      return 'rgba(0,0,0,0.5)';
    }
    hex = hex.replace('#', '');
    const r = parseInt(hex.substring(0, 2), 16);
    const g = parseInt(hex.substring(2, 4), 16);
    const b = parseInt(hex.substring(4, 6), 16);
    alpha = Math.min(1, Math.max(0, alpha));
    return `rgba(${r},${g},${b},${alpha})`;
  }
  const dataBarPremiumAttendance = computed(() => {
    if (getPremiumByDate.value) {
      const dataSet = getPremiumByDate.value.total;
      if (
        useOverviewStore.getPremiumByDate[0].by_service.length === 0 ||
        (dataSet.premium_arrived_payed_by_doctor === 0 &&
          dataSet.premium_arrived_payed_by_system === 0 &&
          dataSet.premium_no_show === 0 &&
          dataSet.premium_delayed_by_patient === 0 &&
          dataSet.premium_delayed_by_doctor === 0)
      ) {
        return {
          labels: ['Žiadne dáta'],
          datasets: [
            {
              data: [1],
              backgroundColor: hexToRgba('#07327a', 0.5)
            }
          ]
        };
      } else if (getPremiumByDate.value) {
        return {
          labels: ['Dorazili včas - označené lekárom', 'Dorazili včas - označené systémom', 'Nedorazili', 'Oneskorenie klienta', 'Oneskorenie lekára'],
          datasets: [
            {
              backgroundColor: [
                'rgba(221, 27, 22, 0.75)',
                'rgba(213, 0, 0, 0.75)',
                'rgba(230, 124, 115, 0.75)',
                'rgba(244, 81, 30, 0.75)',
                'rgba(246, 191, 38, 0.75)',
                'rgba(255, 165, 67, 0.75)'
              ],
              data: [
                dataSet.premium_arrived_payed_by_doctor,
                dataSet.premium_arrived_payed_by_system,
                dataSet.premium_no_show,
                dataSet.premium_delayed_by_patient,
                dataSet.premium_delayed_by_doctor
              ]
            }
          ]
        };
      }
    } else
      return {
        labels: [],
        datasets: [
          {
            data: []
          }
        ]
      };
  });

  const dataPiePremiumDeleted = computed(() => {
    if (getPremiumByDate.value) {
      const dataSet = getPremiumByDate.value.total;
      if (
        useOverviewStore.getPremiumByDate[0].by_service.length === 0 ||
        (dataSet.cancelled_by_user === 0 && dataSet.cancelled_by_system === 0 && dataSet.cancelled_by_client === 0)
      ) {
        return {
          labels: ['Žiadne dáta'],
          datasets: [
            {
              data: [1],
              backgroundColor: hexToRgba('#07327a', 0.5)
            }
          ]
        };
      } else if (getPremiumByDate.value) {
        return {
          labels: ['Zmazané lekárom', 'Zmazané systémom', 'Zmazané klientom'],
          datasets: [
            {
              backgroundColor: ['rgba(221, 27, 22, 0.75)', 'rgba(213, 0, 0, 0.75)', 'rgba(230, 124, 115, 0.75)'],
              data: [dataSet.cancelled_by_user, dataSet.cancelled_by_system, dataSet.cancelled_by_client]
            }
          ]
        };
      }
    } else
      return {
        labels: [],
        datasets: [
          {
            data: []
          }
        ]
      };
  });

  const dataBarFreeAttendance = computed(() => {
    if (getFreeByDate.value) {
      const dataSet = getFreeByDate.value.total;
      if (
        useOverviewStore.getFreeByDate[0].by_service.length === 0 ||
        (dataSet.free_arrived_by_doctor === 0 &&
          dataSet.free_arrived_by_system === 0 &&
          dataSet.free_no_show === 0 &&
          dataSet.free_delayed_by_client === 0 &&
          dataSet.free_delayed_by_doctor === 0)
      ) {
        return {
          labels: ['Žiadne dáta'],
          datasets: [
            {
              data: [1],
              backgroundColor: hexToRgba('#07327a', 0.5)
            }
          ]
        };
      } else if (getFreeByDate.value) {
        return {
          labels: ['Dorazili včas - označené lekárom', 'Dorazili včas - označené systémom', 'Nedorazili', 'Oneskorenie klienta', 'Oneskorenie lekára'],
          datasets: [
            {
              backgroundColor: [
                'rgba(65, 184, 131, 0.75)',
                'rgba(11, 128, 67, 0.75)',
                'rgba(130, 204, 130, 0.75)',
                'rgba(3, 155, 229, 0.75)',
                'rgba(63, 81, 181, 0.75)',
                'rgba(121, 134, 203, 0.75)'
              ],
              data: [
                dataSet.free_arrived_by_doctor,
                dataSet.free_arrived_by_system,
                dataSet.free_no_show,
                dataSet.free_delayed_by_client,
                dataSet.free_delayed_by_doctor
              ]
            }
          ]
        };
      }
    } else
      return {
        labels: [],
        datasets: [
          {
            data: []
          }
        ]
      };
  });

  const dataPieFreeDeleted = computed(() => {
    if (getFreeByDate.value) {
      const dataSet = getFreeByDate.value.total;
      if (useOverviewStore.getFreeByDate[0].by_service.length === 0 || (dataSet.free_removed_by_client === 0 && dataSet.free_removed_by_doctor === 0)) {
        return {
          labels: ['Žiadne dáta'],
          datasets: [
            {
              data: [1],
              backgroundColor: hexToRgba('#07327a', 0.5)
            }
          ]
        };
      } else if (getFreeByDate.value) {
        return {
          labels: ['Zmazané klientom', 'Zmazané lekárom'],
          datasets: [
            {
              backgroundColor: ['rgba(65, 184, 131, 0.75)', 'rgba(11, 128, 67, 0.75)'],
              data: [dataSet.free_removed_by_client, dataSet.free_removed_by_doctor]
            }
          ]
        };
      }
    } else
      return {
        labels: [],
        datasets: [
          {
            data: []
          }
        ]
      };
  });
  const dataBarTotalStats = computed(() => {
    if (
      getTotalStats.value &&
      getTotalStats.value.total_arrived_on_time === 0 &&
      getTotalStats.value.total_delay_by_doctor === 0 &&
      getTotalStats.value.total_delay_by_patient === 0 &&
      getTotalStats.value.total_no_showups === 0
    ) {
      return {
        labels: ['Žiadne dáta'],
        datasets: [
          {
            data: [1],
            backgroundColor: hexToRgba('#07327a', 0.5)
          }
        ]
      };
    } else if (getTotalStats.value)
      return {
        labels: ['Prišli včas', 'Oneskorenie lekára', 'Oneskorenie klienta', 'Nedorazili'],
        datasets: [
          {
            backgroundColor: ['rgba(65, 184, 131, 0.75)', 'rgba(130, 204, 130, 0.75)', 'rgba(3, 155, 229, 0.75)', 'rgba(63, 81, 181, 0.75)'],
            data: [
              getTotalStats.value.total_arrived_on_time,
              getTotalStats.value.total_delay_by_doctor,
              getTotalStats.value.total_delay_by_patient,
              getTotalStats.value.total_no_showups
            ]
          }
        ]
      };
    else
      return {
        labels: [],
        datasets: [
          {
            data: []
          }
        ]
      };
  });

  const dataBarDelayAverage = computed(() => {
    const dataSetFree = getDelayAveragesFree.value;
    const dataSetPremium = getDelayAveragesPaid.value;
    return {
      labels: ['Priemerné oneskorenie lekára', 'Priemerné oneskorenie klienta', 'Mediánové oneskorenie klienta lekára', 'Mediánové oneskorenie klienta'],
      datasets: [
        {
          label: 'Orientačný čas',
          backgroundColor: 'rgba(65, 184, 131, 0.75)',
          data: [parseInt(dataSetFree.doctor_average_delay), 0, 0, 0]
        },
        {
          label: 'Orientačný čas',
          backgroundColor: 'rgba(65, 184, 131, 0.75)',
          data: [0, parseInt(dataSetFree.patient_average_delay), 0, 0]
        },
        {
          label: 'Presný čas',
          backgroundColor: 'rgba(221, 27, 22, 0.75)',
          data: [0, 0, parseInt(dataSetPremium.doctor_average_delay), 0]
        },
        {
          label: 'Presný čas',
          backgroundColor: 'rgba(221, 27, 22, 0.75)',
          data: [0, 0, 0, parseInt(dataSetPremium.patient_average_delay)]
        }
      ]
    };
  });

  const chartOptionsPie = (position) => {
    return {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        datalabels: {
          color: '#000000',
          formatter: (value, context) => {
            const dataPoints = context.chart.data.datasets[0].data;
            if (value === 0) {
              return '';
            }

            function totalSum(total, dataPoint) {
              return total + dataPoint;
            }

            const totalValue = dataPoints.reduce(totalSum, 0);
            const percentageValue = ((value / totalValue) * 100).toFixed(0);
            return [`${percentageValue}%` + ' ' + '(' + value + ')'];
          }
        },
        legend: {
          display: true,
          position: position,
          labels: {
            padding: 15
          }
        }
      }
    };
  };

  const chartOptionsBar = (text, position) => {
    return {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false
        },
        datalabels: {
          color: '#000000',
          display: function (context) {
            return context.dataset.data[context.dataIndex] !== 0;
          }
        }
      },
      scales: {
        y: {
          stacket: true,
          beginAtZero: true,
          title: {
            display: true,
            text: text
          }
        },
        x: {
          stacked: true
        }
      },
      ticks: {
        precision: 0
      }
    };
  };
</script>

<style lang="scss" scoped>
  h1,
  h2 {
    text-align: center;
    line-height: 1.3;
  }

  .flex-center {
    display: flex;
    justify-content: center;
  }

  label {
    font-weight: 600;
    display: flex;
    align-items: center;
  }

  .select-container {
    padding: 0.2em 0.75em;
    border-radius: 3px;
    background: rgba(202, 198, 195, 0.575);
  }

  .select-container:hover {
    background: rgba(202, 198, 195, 0.764);
  }

  .graphs-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    margin-bottom: 5rem;
    gap: 2em;
    margin-right: 1em;
  }

  .graph-desc {
    margin: 0.75em 0;
    text-align: center;
  }

  .graph {
    margin-bottom: 6em;
    margin-left: auto;
    margin-right: auto;
    text-align: center;
    width: 90%;
    height: 350px;
  }

  .table-cointainer {
    margin-bottom: 2em;
  }

  v-table {
    border-collapse: collapse;
  }

  th,
  td {
    text-align: center !important;
    padding: 0.5em;
  }

  thead {
    background: #052e73;
    color: white;
  }

  .tables {
    max-width: 800px;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .table-wrapper {
    width: 100%;
  }

  .table-header {
    margin-bottom: 0.2em;
  }

  .graph-pie {
    margin-bottom: 3em;
    margin-left: auto;
    margin-right: auto;
    text-align: center;
    width: 90%;
  }

  .graph-pie-total {
    margin-top: 2em;
  }

  .graph-charts {
    display: flex;
    flex-wrap: wrap;
    gap: 1em;
    justify-content: center;
    align-items: center;
  }

  .graph-chart {
    flex: 1;
    width: calc(50% - 1em);
  }
  .employee-select {
    min-width: 250px;
    max-width: 400px;
  }
  .select-area-statistics {
    border: none;
    position: relative;
    display: flex;
    justify-content: space-between;
  }
  @media (max-width: 940px) {
    .graph-charts {
      flex-direction: column;
    }

    .graph-chart {
      width: 100%;
    }

    .graph-smaller-legend {
      padding-right: 5.7em;
    }
  }

  @media (min-width: 1800px) {
    .graph-pie {
      width: 70%;
    }

    .graph-pie-total {
      width: 60%;
    }
  }

  @media (min-width: 2000px) {
    .graph-pie {
      max-width: 1500px;
    }
  }

  @media screen and (max-width: 990px) {
    .graph {
      flex-direction: column;
      width: 100%;
    }

    .graph-mob {
      flex-direction: column;
      align-items: center;
      width: 90% !important;
    }

    .table-cointainer {
      padding-right: 1em;
      width: 100%;
    }
  }

  @media screen and (max-width: 400px) {
    .graph-desc {
      font-size: 1em;
      margin-inline: 1em;
    }

    .graph-pie-total {
      width: 100%;
      padding-left: 1em;
      margin-top: 6em;
    }
  }

  @media screen and (max-width: 673px) {
    .tables {
      margin-top: 2em;
    }
    .employee-select {
      margin-right: 1em;
    }
    .select-area-statistics {
      display: block;
    }
    .graph-smaller-legend {
      padding: 0;
    }

    .graph-chart {
      flex: none;
      height: 400px;
      width: 90%;
    }

    .graphs-wrapper {
      gap: 0;
    }

    .graph-pie-total {
      margin-top: 6.5em;
    }
  }

  @media screen and (max-width: 500px) {
    .tables {
      margin-top: 0em;
    }
  }

  /* Enhanced Design Styles */
  .statistics-header {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 12px;
    padding: 2em;
    margin-bottom: 2em;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border: 1px solid #e2e8f0;
  }

  .header-controls {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 2em;
    align-items: start;
  }

  .employee-select-container {
    display: flex;
    flex-direction: column;
    gap: 0.75em;
  }

  .employee-select {
    max-width: 300px;
  }

  .date-picker-container {
    display: flex;
    flex-direction: column;
    gap: 1em;
  }

  .section-title {
    color: #1e293b;
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5em;
  }

  .section-icon {
    color: var(--green);
  }

  .date-picker-wrapper {
    display: flex;
    flex-direction: column;
    gap: 1em;
  }

  .current-range {
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-radius: 8px;
    padding: 1em;
    display: flex;
    flex-direction: column;
    gap: 0.5em;
  }

  .range-label {
    font-weight: 600;
    color: #1e293b;
    font-size: 0.9rem;
  }

  .range-dates {
    font-weight: 600;
    color: var(--blue);
  }

  .date-picker {
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  /* Enhanced Table Styling */
  .table-wrapper {
    background: #ffffff;
    border-radius: 12px;
    padding: 1.5em;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border: 1px solid #e2e8f0;
    margin-bottom: 1.5em;
  }

  .table-header {
    color: #1e293b;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5em;
  }

  /* Responsive Design for Enhanced Header */
  @media (max-width: 768px) {
    .statistics-header {
      padding: 1.5em;
    }

    .header-controls {
      grid-template-columns: 1fr;
      gap: 1.5em;
    }

    .employee-select {
      max-width: 100%;
    }
  }

  @media (max-width: 480px) {
    .statistics-header {
      padding: 1em;
      margin-bottom: 1.5em;
    }

    .current-range {
      padding: 0.75em;
    }

    .section-title {
      font-size: 1.1rem;
    }

    .table-wrapper {
      padding: 1em;
    }

    .table-header {
      font-size: 1.1rem;
    }
  }
</style>
