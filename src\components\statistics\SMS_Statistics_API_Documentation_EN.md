
# SMS Statistics API Documentation

## Overview

This documentation describes new API endpoints for extended SMS statistics in the reservation system. All endpoints are available under the base path `/api/v1/statistics/user/{id_user}/`.

## New Endpoints

### 1. Detailed SMS Statistics by Category

**Endpoint:** `POST /api/v1/statistics/user/{id_user}/smsStatisticsDetailed/`

**Description:** Retrieves detailed SMS statistics broken down by category (doctor/patient/kiosk) for the selected period.

**Parameters:**
- `id_user` (int, path) - User (doctor) ID
- `date_range` (ByDateInput, body) - Period for statistics

**Sample Request:**
```bash
curl -X POST "http://localhost:5000/api/v1/statistics/user/123/smsStatisticsDetailed/"   -H "Content-Type: application/json"   -d '{
    "from_date": "2024-01-01",
    "to_date": "2024-01-31"
  }'
```

**Sample Response:**
```json
{
  "success": true,
  "message": "SMS statistics retrieved successfully",
  "data": {
    "total_sms": 150,
    "total_segments": 180,
    "categories": {
      "doctor": {
        "count": 80,
        "segments": 95,
        "percentage": 53.33
      },
      "patient": {
        "count": 45,
        "segments": 52,
        "percentage": 30.0
      },
      "kiosk": {
        "count": 25,
        "segments": 33,
        "percentage": 16.67
      }
    },
    "period": {
      "from_date": "2024-01-01",
      "to_date": "2024-01-31"
    }
  }
}
```

**Field Explanations:**
- `total_sms` - Total number of SMS messages for the period
- `total_segments` - Total number of SMS segments (160 characters = 1 segment)
- `categories.doctor.count` - Number of SMS sent by the doctor via the admin panel
- `categories.doctor.segments` - Number of segments for doctor SMS
- `categories.doctor.percentage` - Percentage of doctor SMS from the total
- `categories.patient.count` - Number of SMS paid by the patient
- `categories.patient.segments` - Number of segments for patient SMS
- `categories.patient.percentage` - Percentage of patient SMS from the total
- `categories.kiosk.count` - Number of SMS sent by kiosks Ortotech/Klien
- `categories.kiosk.segments` - Number of segments for kiosk SMS
- `categories.kiosk.percentage` - Percentage of kiosk SMS from the total

### 2. SMS Segment Statistics

**Endpoint:** `POST /api/v1/statistics/user/{id_user}/smsSegmentStatistics/`

**Description:** Retrieves SMS segment statistics considering multipart SMS logic by BulkGate (160 characters per segment).

**Parameters:**
- `id_user` (int, path) - User (doctor) ID
- `date_range` (ByDateInput, body) - Period for statistics

**Sample Request:**
```bash
curl -X POST "http://localhost:5000/api/v1/statistics/user/123/smsSegmentStatistics/"   -H "Content-Type: application/json"   -d '{
    "from_date": "2024-01-01",
    "to_date": "2024-01-31"
  }'
```

**Sample Response:**
```json
{
  "success": true,
  "message": "SMS segment statistics retrieved successfully",
  "data": {
    "total_messages": 150,
    "total_segments": 180,
    "average_segments_per_message": 1.2,
    "segment_distribution": {
      "1_segment": 120,
      "2_segments": 25,
      "3_segments": 5
    },
    "categories": {
      "doctor": {
        "messages": 80,
        "segments": 95,
        "avg_segments": 1.19
      },
      "patient": {
        "messages": 45,
        "segments": 52,
        "avg_segments": 1.16
      },
      "kiosk": {
        "messages": 25,
        "segments": 33,
        "avg_segments": 1.32
      }
    }
  }
}
```

**Field Explanations:**
- `total_messages` - Total number of SMS messages for the period
- `total_segments` - Total number of SMS segments (160 characters = 1 segment)
- `average_segments_per_message` - Average number of segments per message
- `segment_distribution.1_segment` - Number of messages with 1 segment (≤160 characters)
- `segment_distribution.2_segments` - Number of messages with 2 segments (161-306 characters)
- `segment_distribution.3_segments` - Number of messages with 3+ segments (>306 characters)
- `categories.doctor.messages` - Number of SMS sent by the doctor
- `categories.doctor.segments` - Number of segments for doctor SMS
- `categories.doctor.avg_segments` - Average number of segments per doctor SMS
- `categories.patient.messages` - Number of SMS paid by the patient
- `categories.patient.segments` - Number of segments for patient SMS
- `categories.patient.avg_segments` - Average number of segments per patient SMS
- `categories.kiosk.messages` - Number of SMS sent by kiosks
- `categories.kiosk.segments` - Number of segments for kiosk SMS
- `categories.kiosk.avg_segments` - Average number of segments per kiosk SMS

### 3. Statistics for Services with Purchased SMS

**Endpoint:** `POST /api/v1/statistics/user/{id_user}/servicesWithSMSPurchasedStatistics/`

**Description:** Retrieves the number of services with purchased and paid SMS packages for the selected period.

**Parameters:**
- `id_user` (int, path) - User (doctor) ID
- `date_range` (ByDateInput, body) - Period for statistics

**Sample Request:**
```bash
curl -X POST "http://localhost:5000/api/v1/statistics/user/123/servicesWithSMSPurchasedStatistics/"   -H "Content-Type: application/json"   -d '{
    "from_date": "2024-01-01",
    "to_date": "2024-01-31"
  }'
```

**Sample Response:**
```json
{
  "success": true,
  "message": "Services with SMS purchased statistics retrieved successfully",
  "data": {
    "total_services": 15,
    "services_with_sms_purchased": 12,
    "services_with_sms_paid": 10,
    "percentage_with_sms": 80.0,
    "percentage_paid": 66.67,
    "period": {
      "from_date": "2024-01-01",
      "to_date": "2024-01-31"
    }
  }
}
```

**Field Explanations:**
- `total_services` - Total number of services for the doctor
- `services_with_sms_purchased` - Number of services with purchased SMS packages
- `services_with_sms_paid` - Number of services with paid SMS packages
- `percentage_with_sms` - Percentage of services with purchased SMS out of total
- `percentage_paid` - Percentage of services with paid SMS out of total
- `period.from_date` - Start date of the statistics period
- `period.to_date` - End date of the statistics period

### 4. Export SMS Data

**Endpoint:** `POST /api/v1/statistics/user/{id_user}/exportSMSData/`

**Description:** Retrieves SMS data for export to Excel for the selected period.

**Parameters:**
- `id_user` (int, path) - User (doctor) ID
- `date_range` (ByDateInput, body) - Period for export

**Sample Request:**
```bash
curl -X POST "http://localhost:5000/api/v1/statistics/user/123/exportSMSData/"   -H "Content-Type: application/json"   -d '{
    "from_date": "2024-01-01",
    "to_date": "2024-01-31"
  }'
```

**Sample Response:**
```json
{
  "success": true,
  "message": "SMS export data retrieved successfully",
  "data": {
    "total_records": 150,
    "records": [
      {
        "timestamp": "2024-01-15T10:30:00",
        "recipient_phone": "+421901234567",
        "message_content": "Your appointment is confirmed for 15.01.2024 at 14:00",
        "segment_count": 1,
        "sms_type": "confirmation",
        "category": "doctor",
        "service_name": "Therapist Consultation",
        "office_name": "Medical Center"
      },
      {
        "timestamp": "2024-01-15T09:15:00",
        "recipient_phone": "+421902345678",
        "message_content": "Reminder: your appointment is tomorrow at 10:00. Please confirm your attendance by replying to this message. Contact us if you have any questions.",
        "segment_count": 2,
        "sms_type": "reminder",
        "category": "patient",
        "service_name": "Dental Check-up",
        "office_name": "Dental Clinic"
      }
    ],
    "period": {
      "from_date": "2024-01-01",
      "to_date": "2024-01-31"
    }
  }
}
```

**Field Explanations:**
- `total_records` - Total number of SMS records for export
- `records` - Array of SMS records with detailed information
- `records[].timestamp` - Date and time of SMS sending (ISO format)
- `records[].recipient_phone` - Recipient's phone number
- `records[].message_content` - Full text of the SMS message
- `records[].segment_count` - Number of SMS segments (160 characters = 1 segment)
- `records[].sms_type` - Type of SMS (confirmation, reminder, cancellation, etc.)
- `records[].category` - SMS category (doctor, patient, kiosk)
- `records[].service_name` - Name of the related service
- `records[].office_name` - Name of the office providing the service
- `period.from_date` - Start date of the export period
- `period.to_date` - End date of the export period

### 5. Download Excel File

**Endpoint:** `POST /api/v1/statistics/user/{id_user}/downloadSMSExcel/`

**Description:** Downloads an Excel file with SMS data for the selected period.

**Parameters:**
- `id_user` (int, path) - User (doctor) ID
- `date_range` (ByDateInput, body) - Period for export

**Sample Request:**
```bash
curl -X POST "http://localhost:5000/api/v1/statistics/user/123/downloadSMSExcel/"   -H "Content-Type: application/json"   -d '{
    "from_date": "2024-01-01",
    "to_date": "2024-01-31"
  }'   --output sms_export.xlsx
```

**Response:** Binary Excel file named like `sms_export_user_123_20240101_to_20240131_20240703_065830.xlsx`

**File Naming Explanation:**
- `sms_export` - Prefix for SMS export
- `user_123` - User (doctor) ID
- `20240101_to_20240131` - Export period (from 01.01.2024 to 31.01.2024)
- `20240703_065830` - Date and time of file creation (03.07.2024 06:58:30)

**Excel File Structure:**
- **Timestamp** - Date and time of SMS sending
- **Recipient Phone** - Recipient's phone number
- **Message Content** - Full text of the SMS message
- **Segment Count** - Number of segments (160 characters = 1 segment)
- **SMS Type** - Type of SMS (confirmation, reminder, cancellation, etc.)
- **Category** - SMS category (doctor, patient, kiosk)
- **Service Name** - Service name
- **Office Name** - Office name

**Excel File Features:**
- Headers highlighted in blue with white text
- Automatic column width adjustment
- Filters for all columns
- Maximum column width limited to 50 characters

## Data Structures

### ByDateInput
```json
{
  "from_date": "2024-01-01",
  "to_date": "2024-01-31"
}
```

### SMSExportRecord
```json
{
  "timestamp": "2024-01-15T10:30:00",
  "recipient_phone": "+421901234567",
  "message_content": "Message text",
  "segment_count": 1,
  "sms_type": "confirmation",
  "category": "doctor",
  "service_name": "Service name",
  "office_name": "Office name"
}
```

## SMS Categories

1. **doctor** - SMS sent by the doctor via the admin panel
2. **patient** - SMS paid by the patient
3. **kiosk** - SMS sent by Ortotech and Klien kiosks

## SMS Types

- **confirmation** - Appointment confirmation
- **reminder** - Appointment reminder
- **cancellation** - Appointment cancellation
- **reschedule** - Appointment reschedule
- **notification** - General notifications

## Segment Counting Logic

SMS segments are calculated using BulkGate logic:
- Up to 160 characters = 1 segment
- 161-306 characters = 2 segments
- 307-459 characters = 3 segments
- And so on...

## Error Codes

- **400** - Invalid date format or period
- **404** - User not found
- **500** - Internal server error

## Testing

### Via Swagger UI
1. Open `http://localhost:5000/docs`
2. Find the "Statistics" section
3. Select the desired endpoint
4. Fill in the parameters and execute the request

### Via curl
```bash
# Test detailed statistics
curl -X POST "http://localhost:5000/api/v1/statistics/user/1/smsStatisticsDetailed/"   -H "Content-Type: application/json"   -d '{"from_date": "2024-01-01", "to_date": "2024-01-31"}'

# Test Excel export
curl -X POST "http://localhost:5000/api/v1/statistics/user/1/downloadSMSExcel/"   -H "Content-Type: application/json"   -d '{"from_date": "2024-01-01", "to_date": "2024-01-31"}'   --output test_export.xlsx
```

### Via Postman
1. Create a new POST request
2. Set the URL: `http://localhost:5000/api/v1/statistics/user/{id_user}/{endpoint}/`
3. Add to Headers: `Content-Type: application/json`
4. In Body (raw JSON) add:
```json
{
  "from_date": "2024-01-01",
  "to_date": "2024-01-31"
}
```

## Notes

- All dates must be in YYYY-MM-DD format
- User ID must exist in the system
- The period cannot exceed 1 year
- Excel files include formatting and filters for convenient analysis
- Data in Excel includes all fields from SMSExportRecord
