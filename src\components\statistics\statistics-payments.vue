<template>
  <div>
    <loading-overlay :loading="loading" />
    <div>
      <!-- Enhanced Header Section -->
      <div class="statistics-header">
        <div class="header-controls">
          <div
            class="employee-select-container"
            v-if="
              employees &&
              (currentUser.role === editRole ||
                currentUser.role === viewRole ||
                currentUser.role === technicalRole ||
                myApiUrl === 'https://test-api.vcakarni.sk/api/v1')
            "
          >
            <v-select
              v-model="selectedEmployee"
              :item-title="(employees) => `${employees.first_name} ${employees.last_name}`"
              :items="employees"
              label="Vyber zamestnanca"
              return-object
              class="employee-select"
              @update:modelValue="onSelectChange(selectedEmployee)"
            ></v-select>
          </div>

          <div class="date-picker-container">
            <h3 class="section-title">Zvoľte obdobie</h3>
            <div class="date-picker-wrapper">
              <section v-if="getCurrentRange" class="current-range">
                <span class="range-label">Aktu<PERSON>lne obdobie:</span>
                <span class="range-dates">
                  {{ getCurrentRange.from_date.slice(0, 10).split('-').reverse().join('.') }} -
                  {{ getCurrentRange.to_date.slice(0, 10).split('-').reverse().join('.') }}
                </span>
              </section>
              <DatePicker v-model="dateRange" color="blue" is-range class="date-picker"></DatePicker>
            </div>
          </div>
        </div>
      </div>

      <section style="margin-right: 1em">
        <div class="graphs-wrapper" v-if="getPaymentsByDate">
          <div class="graph-bar">
            <h3 class="graph-desc">Prehľad všetkých transakcií za zvolené obdobie.</h3>
            <div style="padding-right: 1em; height: 100%; user-select: none">
              <Bar :data="dataBarOverall" :options="chartOptionsBarNoLegendNoTitle" />
            </div>
          </div>

          <div class="graph-bar">
            <h3 class="graph-desc">Prehľad zaplatených platieb za zvolené obdobie.</h3>
            <div style="padding-right: 1em; height: 100%; user-select: none">
              <Bar :data="dataBarPaid" :options="chartOptionsBarNoLegend" />
            </div>
          </div>

          <div class="graph-bar">
            <h3 class="graph-desc">Prehľad vrátených platieb za zvolené obdobie.</h3>
            <div style="padding-right: 1em; height: 100%; user-select: none">
              <Bar :data="dataBarRefund" :options="chartOptionsBarNoLegend" />
            </div>
          </div>

          <div class="graph-bar">
            <h3 class="graph-desc">Počet zakúpených zozanamov čakateľov za zvolené obdobie.</h3>
            <div style="padding-right: 1em; height: 100%; user-select: none">
              <Bar :data="dataBarWaitingList" :options="chartOptionsBar" />
            </div>
          </div>

          <div class="sms-statistics-section">
            <h2 class="sms-main-title">Štatistiky využitia SMS</h2>

            <div class="remaining-sms-info">
              <div class="graph-desc monthly-sms">
                Počet zostávajúcich SMS v tomto mesiaci ({{ currentMonthSlovak }}) je:
                <span style="font-weight: 700; color: var(--blue)">{{ currentSMS }}</span>
              </div>
            </div>

            <!-- Total Paid Services with SMS -->
            <div class="sms-stat-card">
              <h3 class="sms-section-title">
                Celkový počet platených služieb s SMS
                <v-tooltip location="top">
                  <template v-slot:activator="{ props }">
                    <v-icon v-bind="props" class="info-icon" size="small">mdi-information</v-icon>
                  </template>
                  <span>Počet služieb s zakúpenými SMS, ktoré boli vytvorené a úspešne zaplatené v zvolenom období.</span>
                </v-tooltip>
              </h3>
              <div class="sms-stat-value" v-if="getServicesWithSmsStats">
                <span class="stat-number">{{ getServicesWithSmsStats.total_services_with_sms_purchased || 0 }}</span>
                <span class="stat-description">služieb s SMS</span>
              </div>
              <div class="sms-stat-explanation">Počet služieb s zakúpenými SMS, ktoré boli vytvorené a úspešne zaplatené v zvolenom období.</div>
            </div>

            <!-- Total SMS Segments Sent -->
            <div class="sms-stat-card">
              <h3 class="sms-section-title">
                Celkový počet odoslaných SMS segmentov
                <v-tooltip location="top">
                  <template v-slot:activator="{ props }">
                    <v-icon v-bind="props" class="info-icon" size="small">mdi-information</v-icon>
                  </template>
                  <span>
                    Celkový počet SMS segmentov odoslaných v zvolenom období. Správa môže pozostávať z viacerých segmentov na základe jej dĺžky, ako je
                    definované naším SMS poskytovateľom.
                  </span>
                </v-tooltip>
              </h3>
              <div class="sms-stat-value" v-if="getSmsSegmentStats">
                <span class="stat-number">{{ getSmsSegmentStats.total_segments || 0 }}</span>
                <span class="stat-description">celkových segmentov</span>
              </div>
              <div class="sms-stat-explanation">
                Celkový počet SMS segmentov odoslaných v zvolenom období. Správa môže pozostávať z viacerých segmentov na základe jej dĺžky, ako je definované
                naším SMS poskytovateľom.
              </div>
            </div>

            <!-- Breakdown by Source -->
            <div class="sms-breakdown-section">
              <h3 class="sms-section-title">Rozdelenie podľa zdroja</h3>

              <div class="graph-bar">
                <h4 class="graph-desc">
                  SMS od lekárov
                  <v-tooltip location="top">
                    <template v-slot:activator="{ props }">
                      <v-icon v-bind="props" class="info-icon" size="small">mdi-information</v-icon>
                    </template>
                    <span>
                      Správy odoslané v dôsledku akcií lekára v administrátorskom rozhraní, kde bolo povolené SMS upozornenie pacienta. Zahŕňa pripomienky
                      termínov, potvrdenia atď.
                    </span>
                  </v-tooltip>
                </h4>
                <div class="sms-stat-explanation">
                  Správy odoslané v dôsledku akcií lekára v administrátorskom rozhraní, kde bolo povolené SMS upozornenie pacienta. Zahŕňa pripomienky termínov,
                  potvrdenia atď.
                </div>
                <div style="padding-right: 1em; height: 100%; user-select: none">
                  <Bar :data="dataBarDoctorSms" :options="chartOptionsBar" />
                </div>
              </div>

              <div class="graph-bar">
                <h4 class="graph-desc">
                  SMS hradené pacientom
                  <v-tooltip location="top">
                    <template v-slot:activator="{ props }">
                      <v-icon v-bind="props" class="info-icon" size="small">mdi-information</v-icon>
                    </template>
                    <span>SMS správy odoslané ako súčasť servisného balíka, za ktorý pacient zaplatil priamo.</span>
                  </v-tooltip>
                </h4>
                <div class="sms-stat-explanation">SMS správy odoslané ako súčasť servisného balíka, za ktorý pacient zaplatil priamo.</div>
                <div style="padding-right: 1em; height: 100%; user-select: none">
                  <Bar :data="dataBarPatientSms" :options="chartOptionsBar" />
                </div>
              </div>

              <div class="graph-bar">
                <h4 class="graph-desc">
                  SMS z kioskov/systému
                  <v-tooltip location="top">
                    <template v-slot:activator="{ props }">
                      <v-icon v-bind="props" class="info-icon" size="small">mdi-information</v-icon>
                    </template>
                    <span>Správy odoslané automaticky samoobslužnými kioskami (momentálne pre Ortotech a Klien). Tieto nie sú spojené s aktivitou lekára.</span>
                  </v-tooltip>
                </h4>
                <div class="sms-stat-explanation">
                  Správy odoslané automaticky samoobslužnými kioskami (momentálne pre Ortotech a Klien). Tieto nie sú spojené s aktivitou lekára.
                </div>
                <div style="padding-right: 1em; height: 100%; user-select: none">
                  <Bar :data="dataBarKioskSms" :options="chartOptionsBar" />
                </div>
              </div>

              <div class="graph-bar">
                <h4 class="graph-desc">Prehľad všetkých SMS podľa kategórií</h4>
                <div style="padding-right: 1em; height: 100%; user-select: none">
                  <Bar :data="dataBarSmsOverview" :options="chartOptionsBar" />
                </div>
              </div>
            </div>

            <!-- Enhanced SMS Export Section -->
            <div class="sms-export-section">
              <div class="export-header">
                <h3 class="sms-section-title">
                  <v-icon class="section-icon">mdi-download</v-icon>
                  Export SMS správ pre lekára
                  <v-tooltip location="top">
                    <template v-slot:activator="{ props }">
                      <v-icon v-bind="props" class="info-icon" size="small">mdi-information</v-icon>
                    </template>
                    <span>
                      Stiahnite všetky SMS správy odoslané pod konkrétnym lekárom za zvolené obdobie. Export obsahuje obsah správy, časovú pečiatku, príjemcu a
                      počet segmentov.
                    </span>
                  </v-tooltip>
                </h3>
                <div class="sms-stat-explanation">
                  Stiahnite všetky SMS správy odoslané pod konkrétnym lekárom za zvolené obdobie. Export obsahuje obsah správy, časovú pečiatku, príjemcu a
                  počet segmentov.
                </div>
              </div>

              <div class="export-controls-grid">
                <div class="export-row">
                  <div class="export-control-group">
                    <v-select
                      v-model="selectedExportEmployee"
                      :item-title="(employee) => `${employee.first_name} ${employee.last_name}`"
                      :items="employees"
                      label="Vyber lekára pre export"
                      return-object
                      class="employee-select"
                    ></v-select>
                  </div>

                  <div class="export-control-group">
                    <label class="control-label">Obdobie pre export:</label>
                    <DatePicker v-model="exportDateRange" color="blue" is-range class="export-date-picker"></DatePicker>
                  </div>
                </div>


   <div class="date-picker-container">
            <h3 class="section-title">Zvoľte obdobie</h3>
            <div class="date-picker-wrapper">
              <section v-if="getCurrentRange" class="current-range">
                <span class="range-label">Aktuálne obdobie:</span>
                <span class="range-dates">
                  {{ getCurrentRange.from_date.slice(0, 10).split('-').reverse().join('.') }} -
                  {{ getCurrentRange.to_date.slice(0, 10).split('-').reverse().join('.') }}
                </span>
              </section>
              <DatePicker v-model="dateRange" color="blue" is-range class="date-picker"></DatePicker>
            </div>
          </div>


                <div class="export-actions">
                  <v-btn
                    @click="previewSmsExport"
                    :disabled="!selectedExportEmployee || exportLoading"
                    :loading="exportLoading"
                    variant="outlined"
                    color="primary"
                    prepend-icon="mdi-eye"
                    class="export-btn"
                  >
                    Náhľad dát
                  </v-btn>

                  <v-btn
                    @click="downloadSmsExport"
                    :disabled="!selectedExportEmployee || exportLoading"
                    :loading="exportLoading"
                    variant="elevated"
                    color="success"
                    prepend-icon="mdi-download"
                    class="export-btn"
                  >
                    Stiahnuť Excel
                  </v-btn>
                </div>
              </div>
              <!-- Enhanced Export Preview with Pagination -->
              <div v-if="exportPreviewData && exportPreviewData.length > 0" class="export-preview">
                <div class="preview-header">
                  <h4>
                    <v-icon class="preview-icon">mdi-table-eye</v-icon>
                    Náhľad exportovaných dát
                  </h4>
                  <v-chip color="primary" variant="outlined">{{ exportPreviewData.length }} záznamov</v-chip>
                </div>

                <v-data-table
                  :headers="previewHeaders"
                  :items="exportPreviewData"
                  :items-per-page="itemsPerPage"
                  :items-per-page-options="itemsPerPageOptions"
                  v-model:page="currentPage"
                  class="preview-data-table"
                  density="comfortable"
                  :loading="exportLoading"
                  loading-text="Načítavam SMS dáta..."
                  no-data-text="Žiadne SMS dáta na zobrazenie"
                  items-per-page-text="Záznamov na stránku:"
                  page-text="{0}-{1} z {2}"
                >
                  <template v-slot:item.timestamp="{ item }">
                    <span class="timestamp-cell">{{ formatDateTime(item.timestamp) }}</span>
                  </template>

                  <template v-slot:item.message_content="{ item }">
                    <div class="message-content-cell">
                      <v-tooltip location="top" max-width="400">
                        <template v-slot:activator="{ props }">
                          <span v-bind="props" class="message-preview">
                            {{ item.message_content }}
                          </span>
                        </template>
                        <span>{{ item.message_content }}</span>
                      </v-tooltip>
                    </div>
                  </template>

                  <template v-slot:item.category="{ item }">
                    <v-chip :color="getCategoryColor(item.category)" size="small" variant="tonal">
                      {{ getCategoryLabel(item.category) }}
                    </v-chip>
                  </template>

                  <template v-slot:item.service_name="{ item }">
                    <span>{{ item.service_name || '-' }}</span>
                  </template>

                  <template v-slot:item.office_name="{ item }">
                    <span>{{ item.office_name || '-' }}</span>
                  </template>
                </v-data-table>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>
</template>

<script setup>
  import { DatePicker } from 'v-calendar';
  import 'v-calendar/dist/style.css';
  import { computed, onMounted, reactive, ref, watch } from 'vue';

  import { Bar, Doughnut } from 'vue-chartjs';
  import { ArcElement, BarElement, CategoryScale, Chart as ChartJS, Colors, Legend, LinearScale, LineElement, PointElement, Title, Tooltip } from 'chart.js';
  import ChartDataLabels from 'chartjs-plugin-datalabels';
  import { useOverview } from '../../stores/statistics.js';
  import '../../assets/css/responsive-design.css';
  import { useEmployee } from '../../stores/employee.js';
  import { useUser } from '../../stores/user.js';
  import { useOffice } from '../../stores/office.js';
  import { handleToastError, handleToastSuccess } from '../../utility/notification.js';

  const useOfficeStore = useOffice();
  ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, ArcElement, PointElement, LineElement, Colors, ChartDataLabels);

  const useOverviewStore = useOverview();

  const myApiUrl = import.meta.env.VITE_API_URL;
  let from, to;
  const loading = ref(true);
  const currentOffice = ref();
  const currentSMS = ref();
  const currentMonthSlovak = ref();

  // SMS Export variables
  const selectedExportEmployee = ref();
  const exportDateRange = ref({
    start: new Date(),
    end: new Date()
  });
  const exportLoading = ref(false);
  const exportPreviewData = ref(null);

  // Pagination variables
  const currentPage = ref(1);
  const itemsPerPage = ref(10);
  const itemsPerPageOptions = [
    { value: 5, title: '5' },
    { value: 10, title: '10' },
    { value: 25, title: '25' },
    { value: 50, title: '50' },
    { value: -1, title: 'Všetky' }
  ];

  // Table headers for preview
  const previewHeaders = [
    { title: 'Dátum a čas', key: 'timestamp', sortable: true, width: '140px' },
    { title: 'Telefón', key: 'recipient_phone', sortable: true, width: '120px' },
    { title: 'Obsah správy', key: 'message_content', sortable: false, width: '300px' },
    { title: 'Segmenty', key: 'segment_count', sortable: true, width: '80px' },
    { title: 'Typ SMS', key: 'sms_type', sortable: true, width: '150px' },
    { title: 'Kategória', key: 'category', sortable: true, width: '100px' },
    { title: 'Služba', key: 'service_name', sortable: true, width: '150px' },
    { title: 'Ambulancia', key: 'office_name', sortable: true, width: '150px' }
  ];
  onMounted(async () => {
    loading.value = true;
    currentOffice.value = localStorage.getItem('office');
    if (!useEmployeeStore.getEmployee.length) {
      //  await useEmployeeStore.fetchEmployee()
    }
    if (changedUser.value) {
      selectedEmployee.value = changedUser.value;
    } else if (currentUser.value) {
      if (useUserStore.findActiveUser(currentUser.value)) {
        selectedEmployee.value = currentUser.value;
      } else {
        selectedEmployee.value = employees.value[0];
      }
    }
    await Promise.all([
      useOverviewStore.fetchPremiumByDate(from, to, selectedEmployee.value.id_user),
      useOverviewStore.fetchPaymentPaid(from, to, selectedEmployee.value.id_user),
      useOverviewStore.fetchPaymentRefunded(from, to, selectedEmployee.value.id_user),
      useOverviewStore.fetchPayment(from, to, selectedEmployee.value.id_user),
      useOverviewStore.fetchSms(from, to, selectedEmployee.value.id_user),
      useOverviewStore.fetchSmsSegmentStats(from, to, selectedEmployee.value.id_user),
      useOverviewStore.fetchServicesWithSmsStats(from, to, selectedEmployee.value.id_user),
      useOverviewStore.fetchWaitingListPurchase(from, to, selectedEmployee.value.id_user)
    ]);
    const response = await useOfficeStore.checkRemainingSms(currentOffice.value);
    if (response && response.status === 200) {
      const currentMonthIndex = new Date().getMonth();
      currentMonthSlovak.value = monthNamesSlovak[currentMonthIndex];
      currentSMS.value = response.data.data;
    }

    loading.value = false;

    // Set default export user to current selected user
    if (selectedEmployee.value) {
      selectedExportEmployee.value = selectedEmployee.value;
    }
  });
  const monthNamesSlovak = ['Január', 'Február', 'Marec', 'Apríl', 'Máj', 'Jún', 'Júl', 'August', 'September', 'Október', 'November', 'December'];
  const viewRole = 'System operator';
  const editRole = 'Head of office';
  const technicalRole = 'Technical employee';
  const selectedEmployee = ref();
  const employees = computed(() => useEmployeeStore.getEmployee);
  const currentUser = computed(() => {
    if (useEmployeeStore.currentlyLoggedUser[0]) {
      return useEmployeeStore.currentlyLoggedUser[0];
    } else return [];
  });
  const onSelectChange = async (myValue) => {
    loading.value = true;
    if (myValue) {
      let start, end;
      start = dateRange.value.start.toISOString().slice(0, 10).split('-').join('-');
      end = dateRange.value.end.toISOString().slice(0, 10).split('-').join('-');
      await Promise.all([
        useOverviewStore.fetchPremiumByDate(start, end, myValue.id_user),
        useOverviewStore.fetchPaymentPaid(start, end, myValue.id_user),
        useOverviewStore.fetchPaymentRefunded(start, end, myValue.id_user),
        useOverviewStore.fetchPayment(start, end, myValue.id_user),
        useOverviewStore.fetchSms(start, end, myValue.id_user),
        useOverviewStore.fetchSmsSegmentStats(start, end, myValue.id_user),
        useOverviewStore.fetchServicesWithSmsStats(start, end, myValue.id_user),
        useOverviewStore.fetchWaitingListPurchase(start, end, myValue.id_user)
      ]);
      await useUserStore.setCurrentUser(myValue);
    }
    loading.value = false;
  };
  const changedUser = computed(() => {
    if (useUserStore.changedUser[0]) {
      return useUserStore.changedUser[0];
    } else return null;
  });
  const getOverview = computed(() => {
    return useOverviewStore.getOverview[0];
  });

  const overview = reactive([]);

  const date = ref(new Date());

  const dateRange = ref({
    start: new Date(),
    end: date.value
  });

  watch(
    () => useOverviewStore.getOverview[0],
    (newOverview) => {
      if (newOverview) {
        overview.value = newOverview;
      }
    }
  );

  // Watch for changes in selected employee and update export employee
  watch(
    () => selectedEmployee.value,
    (newEmployee) => {
      if (newEmployee && !selectedExportEmployee.value) {
        selectedExportEmployee.value = newEmployee;
      }
    }
  );

  watch(dateRange, () => {
    getNewDates();
  });

  const getNewDates = () => {
    parseDates(dateRange.value);
  };

  const parseDates = async (myDates) => {
    loading.value = true;
    let start, end;
    start = myDates.start.toISOString().slice(0, 10).split('-').join('-');
    end = myDates.end.toISOString().slice(0, 10).split('-').join('-');
    await Promise.all([
      useOverviewStore.fetchPremiumByDate(start, end, selectedEmployee.value.id_user),
      useOverviewStore.fetchPaymentPaid(start, end, selectedEmployee.value.id_user),
      useOverviewStore.fetchPaymentRefunded(start, end, selectedEmployee.value.id_user),
      useOverviewStore.fetchPayment(start, end, selectedEmployee.value.id_user),
      useOverviewStore.fetchSms(start, end, selectedEmployee.value.id_user),
      useOverviewStore.fetchSmsSegmentStats(start, end, selectedEmployee.value.id_user),
      useOverviewStore.fetchServicesWithSmsStats(start, end, selectedEmployee.value.id_user),
      useOverviewStore.fetchWaitingListPurchase(start, end, selectedEmployee.value.id_user)
    ]);
    loading.value = false;
  };

  const getPremiumByDate = computed(() => {
    return useOverviewStore.getPremiumByDate[0];
  });
  const getPaymentsByDate = computed(() => {
    return useOverviewStore.getPaymentStats[0];
  });
  const getPaymentsPaidByDate = computed(() => {
    return useOverviewStore.getPaymentPaid[0];
  });
  const getPaymentsRefundedByDate = computed(() => {
    return useOverviewStore.getPaymentRefunded[0];
  });

  const getSmsByDate = computed(() => {
    return useOverviewStore.getSmsStats[0];
  });
  const getSmsSegmentStats = computed(() => {
    return useOverviewStore.getSmsSegmentStats[0];
  });
  const getServicesWithSmsStats = computed(() => {
    return useOverviewStore.getServicesWithSmsStats[0];
  });
  const getSmsExportData = computed(() => {
    return useOverviewStore.getSmsExportData[0];
  });
  const getWaitingListPayment = computed(() => {
    return useOverviewStore.getWaitingListPayment[0];
  });

  const getCheapWaitingList = computed(() => {
    if (getWaitingListPayment.value) {
      return useOverviewStore.getWaitingListPayment[0].category['1,5'];
    } else return [];
  });

  const getMiddleWaitingList = computed(() => {
    if (getWaitingListPayment.value) {
      return useOverviewStore.getWaitingListPayment[0].category['2,0'];
    } else return [];
  });
  const getExpensiveWaitingList = computed(() => {
    if (getWaitingListPayment.value) {
      return useOverviewStore.getWaitingListPayment[0].category['2,5'];
    } else return [];
  });
  const getOverallWaitingList = computed(() => {
    if (getWaitingListPayment.value) {
      return (
        useOverviewStore.getWaitingListPayment[0].category['1,5'] +
        useOverviewStore.getWaitingListPayment[0].category['2,0'] +
        useOverviewStore.getWaitingListPayment[0].category['2,5']
      );
    } else return [];
  });
  const useEmployeeStore = useEmployee();
  const useUserStore = useUser();
  const getCurrentRange = computed(() => {
    return useOverviewStore.range[0];
  });

  const dataBarRefund = computed(() => {
    if (getPaymentsRefundedByDate.value && getPaymentsRefundedByDate.value.category) {
      const category = getPaymentsRefundedByDate.value.category;

      // Extract numerical keys and sort them
      const numericalKeys = Object.keys(category)
        .map(Number)
        .sort((a, b) => a - b);

      // Filter out keys with a value of 0
      const validKeys = numericalKeys.filter((key) => category[key] !== 0);

      // Create data array and labels using valid keys
      const labels = validKeys.map((key) => `${key} €`);
      const data = validKeys.map((key) => category[key]);

      // Define colors and create a dynamic background color array
      const baseColors = [
        'rgba(255, 99, 132, 0.5)',
        'rgba(54, 162, 235, 0.5)',
        'rgba(255, 205, 86, 0.5)',
        'rgba(75, 192, 192, 0.5)',
        'rgba(153, 102, 255, 0.5)',
        'rgba(255, 159, 64, 0.5)',
        'rgba(201, 203, 207, 0.5)',
        'rgba(102, 255, 178, 0.5)',
        'rgba(255, 153, 204, 0.5)',
        'rgba(255, 204, 153, 0.5)'
      ];
      const backgroundColors = validKeys.map((_, index) => baseColors[index % baseColors.length]);

      return {
        labels: labels,
        datasets: [
          {
            backgroundColor: backgroundColors,
            data: data
          }
        ]
      };
    } else {
      return {
        labels: ['5 €', '10 €', '15 €', '20 €', '25 €', '30 €'],
        datasets: [
          {
            backgroundColor: [
              'rgba(255, 99, 132, 0.5)',
              'rgba(54, 162, 235, 0.5)',
              'rgba(255, 205, 86, 0.5)',
              'rgba(75, 192, 192, 0.5)',
              'rgba(153, 102, 255, 0.5)',
              'rgba(255, 159, 64, 0.5)'
            ],
            data: []
          }
        ]
      };
    }
  });

  const dataBarPaid = computed(() => {
    if (getPaymentsPaidByDate.value && getPaymentsPaidByDate.value.category) {
      const category = getPaymentsPaidByDate.value.category;

      // Extract numerical keys and sort them
      const numericalKeys = Object.keys(category)
        .map(Number)
        .sort((a, b) => a - b);

      // Filter out keys with a value of 0
      const validKeys = numericalKeys.filter((key) => category[key] !== 0);

      // Create data array and labels using valid keys
      const labels = validKeys.map((key) => `${key} €`);
      const data = validKeys.map((key) => category[key]);

      // Define colors and create a dynamic background color array
      const baseColors = [
        'rgba(255, 99, 132, 0.5)',
        'rgba(54, 162, 235, 0.5)',
        'rgba(255, 205, 86, 0.5)',
        'rgba(75, 192, 192, 0.5)',
        'rgba(153, 102, 255, 0.5)',
        'rgba(255, 159, 64, 0.5)',
        'rgba(201, 203, 207, 0.5)',
        'rgba(102, 255, 178, 0.5)',
        'rgba(255, 153, 204, 0.5)',
        'rgba(255, 204, 153, 0.5)'
      ];
      const backgroundColors = validKeys.map((_, index) => baseColors[index % baseColors.length]);

      return {
        labels: labels,
        datasets: [
          {
            backgroundColor: backgroundColors,
            data: data
          }
        ]
      };
    } else {
      return {
        labels: ['5 €', '10 €', '15 €', '20 €', '25 €', '30 €'],
        datasets: [
          {
            backgroundColor: [
              'rgba(255, 99, 132, 0.5)',
              'rgba(54, 162, 235, 0.5)',
              'rgba(255, 205, 86, 0.5)',
              'rgba(75, 192, 192, 0.5)',
              'rgba(153, 102, 255, 0.5)',
              'rgba(255, 159, 64, 0.5)'
            ],
            data: []
          }
        ]
      };
    }
  });

  const dataBarOverall = computed(() => {
    if (getPaymentsByDate.value) {
      return {
        labels: ['Uhradené', 'Uhradené kreditmi', 'Refundovane kredity', 'Celkovo k úhrade'],
        datasets: [
          {
            backgroundColor: ['rgba(255, 99, 132, 0.5)', 'rgba(54, 162, 235, 0.5)', 'rgba(255, 205, 86, 0.5)', 'rgba(75, 192, 192, 0.5)'],
            data: [
              getPaymentsByDate.value.payed_payments,
              getPaymentsByDate.value.payed_payments_by_credits,
              getPaymentsByDate.value.refunded_payments,
              getPaymentsByDate.value.total_payments
            ]
          }
        ]
      };
    } else
      return {
        labels: ['Uhradené', 'Uhradené kreditmi', 'Refundovane kredity', 'Celkovo k úhrade'],
        datasets: [
          {
            backgroundColor: ['rgba(255, 99, 132, 0.5)', 'rgba(54, 162, 235, 0.5)', 'rgba(255, 205, 86, 0.5)', 'rgba(75, 192, 192, 0.5)'],
            data: []
          }
        ]
      };
  });

  function hexToRgba(hex, alpha) {
    if (!hex) {
      return 'rgba(0,0,0,0.5)';
    }
    hex = hex.replace('#', '');
    const r = parseInt(hex.substring(0, 2), 16);
    const g = parseInt(hex.substring(2, 4), 16);
    const b = parseInt(hex.substring(4, 6), 16);
    alpha = Math.min(1, Math.max(0, alpha));
    return `rgba(${r},${g},${b},${alpha})`;
  }
  const dataBarPremium = computed(() => {
    if (getPremiumByDate.value) {
      const dataSet = getPremiumByDate.value.total;
      if (
        useOverviewStore.getPremiumByDate[0].by_service.length === 0 ||
        (dataSet.premium_payed === 0 && dataSet.premium_refunded === 0 && dataSet.premium_arrived_payed === 0 && dataSet.premium_arrived_refunded === 0)
      ) {
        return {
          labels: ['Žiadne dáta'],
          datasets: [
            {
              data: [1],
              backgroundColor: hexToRgba('#07327a', 0.5)
            }
          ]
        };
      } else if (getPremiumByDate.value) {
        return {
          labels: ['Zaplatené', 'Vrátené', 'Dorazili so zaplatením', 'Dorazili s navrátením'],
          datasets: [
            {
              backgroundColor: ['rgba(213, 0, 0, 0.75)', 'rgba(230, 124, 115, 0.75)', 'rgba(244, 81, 30, 0.75)', 'rgba(246, 191, 38, 0.75)'],
              data: [dataSet.premium_payed, dataSet.premium_refunded, dataSet.premium_arrived_payed, dataSet.premium_arrived_refunded]
            }
          ]
        };
      }
    } else
      return {
        labels: [],
        datasets: [
          {
            data: []
          }
        ]
      };
  });

  const dataBarDoctorSms = computed(() => {
    if (!getSmsByDate.value && !getSmsSegmentStats.value) {
      return {
        labels: ['Žiadne dáta'],
        datasets: [
          {
            label: 'Žiadne dáta',
            data: [0],
            backgroundColor: 'rgba(200, 200, 200, 0.5)'
          }
        ]
      };
    }

    const doctorSmsCount = getSmsByDate.value?.sent_by_doctor || 0;
    const doctorSegments = getSmsSegmentStats.value?.doctor_sms_segments || 0;

    return {
      labels: ['Počet správ', 'Počet segmentov'],
      datasets: [
        {
          label: 'SMS od lekárov',
          backgroundColor: 'rgba(54, 162, 235, 0.7)',
          data: [doctorSmsCount, doctorSegments]
        }
      ]
    };
  });

  const dataBarPatientSms = computed(() => {
    if (!getSmsByDate.value && !getSmsSegmentStats.value) {
      return {
        labels: ['Žiadne dáta'],
        datasets: [
          {
            label: 'Žiadne dáta',
            data: [0],
            backgroundColor: 'rgba(200, 200, 200, 0.5)'
          }
        ]
      };
    }

    const patientSmsCount = getSmsByDate.value?.payed_by_client || 0;
    const patientSegments = getSmsSegmentStats.value?.patient_paid_segments || 0;

    return {
      labels: ['Počet správ', 'Počet segmentov'],
      datasets: [
        {
          label: 'SMS hradené pacientom',
          backgroundColor: 'rgba(255, 159, 64, 0.7)',
          data: [patientSmsCount, patientSegments]
        }
      ]
    };
  });

  const dataBarKioskSms = computed(() => {
    if (!getSmsByDate.value && !getSmsSegmentStats.value) {
      return {
        labels: ['Žiadne dáta'],
        datasets: [
          {
            label: 'Žiadne dáta',
            data: [0],
            backgroundColor: 'rgba(200, 200, 200, 0.5)'
          }
        ]
      };
    }

    // For kiosk SMS, we'll use the remaining SMS that aren't doctor or patient
    const totalSms = getSmsByDate.value?.total_sent || 0;
    const doctorSms = getSmsByDate.value?.sent_by_doctor || 0;
    const patientSms = getSmsByDate.value?.payed_by_client || 0;
    const kioskSmsCount = Math.max(0, totalSms - doctorSms - patientSms);
    const kioskSegments = getSmsSegmentStats.value?.kiosk_system_segments || 0;

    return {
      labels: ['Počet správ', 'Počet segmentov'],
      datasets: [
        {
          label: 'SMS z kioskov/systému',
          backgroundColor: 'rgba(153, 102, 255, 0.7)',
          data: [kioskSmsCount, kioskSegments]
        }
      ]
    };
  });

  const dataBarSmsOverview = computed(() => {
    if (!getSmsByDate.value && !getSmsSegmentStats.value) {
      return {
        labels: ['Žiadne dáta'],
        datasets: [
          {
            label: 'Žiadne dáta',
            data: [0],
            backgroundColor: 'rgba(200, 200, 200, 0.5)'
          }
        ]
      };
    }

    const doctorSms = getSmsByDate.value?.sent_by_doctor || 0;
    const patientSms = getSmsByDate.value?.payed_by_client || 0;
    const totalSms = getSmsByDate.value?.total_sent || 0;
    const kioskSms = Math.max(0, totalSms - doctorSms - patientSms);

    const doctorSegments = getSmsSegmentStats.value?.doctor_sms_segments || 0;
    const patientSegments = getSmsSegmentStats.value?.patient_paid_segments || 0;
    const kioskSegments = getSmsSegmentStats.value?.kiosk_system_segments || 0;

    return {
      labels: ['Lekári', 'Pacienti', 'Kiosky/Systém'],
      datasets: [
        {
          label: 'Počet správ',
          backgroundColor: 'rgba(54, 162, 235, 0.7)',
          data: [doctorSms, patientSms, kioskSms]
        },
        {
          label: 'Počet segmentov',
          backgroundColor: 'rgba(255, 99, 132, 0.7)',
          data: [doctorSegments, patientSegments, kioskSegments]
        }
      ]
    };
  });

  const dataBarWaitingList = computed(() => {
    return {
      labels: ['Všetky čakacie zoznamy', '1,5 €', '2 €', '2,5 €'],
      datasets: [
        {
          label: ['Všetky čakacie zoznamy'],
          backgroundColor: 'rgba(255, 99, 132, 0.5)',
          data: [getOverallWaitingList.value, 0, 0, 0]
        },
        {
          label: ['1,5 €'],
          backgroundColor: 'rgba(54, 162, 235, 0.5)',
          data: [0, getCheapWaitingList.value, 0, 0]
        },
        {
          label: ['2 €'],
          backgroundColor: 'rgba(255, 205, 86, 0.5)',
          data: [0, 0, getMiddleWaitingList.value, 0]
        },
        {
          label: ['2,5 €'],
          backgroundColor: 'rgba(75, 192, 192, 0.5)',
          data: [0, 0, 0, getExpensiveWaitingList.value]
        }
      ]
    };
  });

  const chartOptionsBarNoLegend = computed(() => {
    return barOptions(true);
  });

  const chartOptionsBarNoLegendNoTitle = computed(() => {
    return barOptions(false);
  });
  const barOptions = (title) => {
    const options = {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false
        },
        datalabels: {
          color: '#000000',
          display: function (context) {
            return context.dataset.data[context.dataIndex] !== 0;
          }
        }
      },
      scales: {
        y: {
          beginAtZero: true
        }
      },
      ticks: {
        precision: 0
      }
    };

    if (title) {
      options.scales.x = {
        title: {
          display: true,
          text: 'cenová kategória',
          padding: {
            top: 20
          }
        }
      };
    }

    return options;
  };

  const chartOptionsBar = computed(() => {
    return {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: true
        },

        datalabels: {
          color: '#000000',
          display: function (context) {
            return context.dataset.data[context.dataIndex] !== 0;
          }
        }
      },
      scales: {
        y: {
          stacked: true,
          beginAtZero: true
        },
        x: {
          stacked: true
        }
      },
      ticks: {
        precision: 0
      }
    };
  });
  const chartOptionsPie = computed(() => {
    return {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        datalabels: {
          color: '#000000',
          formatter: (value, context) => {
            const dataPoints = context.chart.data.datasets[0].data;
            if (value === 0) {
              return '';
            }

            function totalSum(total, dataPoint) {
              return total + dataPoint;
            }

            const totalValue = dataPoints.reduce(totalSum, 0);
            const percentageValue = ((value / totalValue) * 100).toFixed(0);
            return [`${percentageValue}%` + ' ' + '(' + value + ')'];
          }
        },
        legend: {
          display: true,
          position: 'top',
          labels: {
            padding: 18
          }
        }
      }
    };
  });

  const formatDate = (date) => {
    if (!date) return '';
    const d = new Date(date);
    const day = String(d.getDate()).padStart(2, '0');
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const year = d.getFullYear();
    return `${year}-${month}-${day}`; // Changed to YYYY-MM-DD format for API
  };

  const formatDateTime = (timestamp) => {
    if (!timestamp) return '';
    const d = new Date(timestamp);
    const day = String(d.getDate()).padStart(2, '0');
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const year = d.getFullYear();
    const hours = String(d.getHours()).padStart(2, '0');
    const minutes = String(d.getMinutes()).padStart(2, '0');
    return `${day}.${month}.${year} ${hours}:${minutes}`;
  };

  const getCategoryLabel = (category) => {
    const labels = {
      doctor: 'Lekár',
      patient: 'Pacient',
      kiosk: 'Kiosk/Systém'
    };
    return labels[category] || category;
  };

  const getCategoryColor = (category) => {
    const colors = {
      doctor: 'blue',
      patient: 'orange',
      kiosk: 'purple'
    };
    return colors[category] || 'grey';
  };

  const previewSmsExport = async () => {
    if (!selectedExportEmployee.value) return;

    exportLoading.value = true;
    try {
      const startDate = formatDate(exportDateRange.value.start);
      const endDate = formatDate(exportDateRange.value.end);

      await useOverviewStore.fetchSmsExportData(startDate, endDate, selectedExportEmployee.value.id_user);
      // getSmsExportData.value is an array containing the data object
      exportPreviewData.value = getSmsExportData.value || null;
    } catch (error) {
      console.error('Error previewing SMS export:', error);
    } finally {
      exportLoading.value = false;
    }
  };

  const downloadSmsExport = async () => {
    if (!selectedExportEmployee.value) return;

    exportLoading.value = true;
    try {
      const startDate = formatDate(exportDateRange.value.start);
      const endDate = formatDate(exportDateRange.value.end);

      await useOverviewStore.downloadSmsExcel(startDate, endDate, selectedExportEmployee.value.id_user);
    } catch (error) {
      console.error('Error downloading SMS export:', error);
    } finally {
      exportLoading.value = false;
    }
  };
</script>

<style scoped>
  h1,
  h2 {
    text-align: center;
    line-height: 1.3;
  }

  .flex-center {
    display: flex;
    justify-content: center;
  }

  label {
    font-weight: 600;
    display: flex;
    align-items: center;
  }

  .select-container {
    padding: 0.2em 0.75em;
    border-radius: 3px;
    background: rgba(202, 198, 195, 0.575);
  }

  .select-container:hover {
    background: rgba(202, 198, 195, 0.764);
  }

  .select-area-statistics {
    border: none;
    position: relative;
    display: flex;
    justify-content: space-between;
  }

  /* Enhanced Header Styles */
  .statistics-header {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 12px;
    padding: 2em;
    margin-bottom: 2em;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border: 1px solid #e2e8f0;
  }

  .header-controls {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 2em;
    align-items: start;
  }

  .employee-select-container {
    display: flex;
    flex-direction: column;
    gap: 0.75em;
  }

  .employee-select {
    max-width: 300px;
  }

  .date-picker-container {
    display: flex;
    flex-direction: column;
    gap: 1em;
  }

  .section-title {
    color: #1e293b;
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5em;
  }

  .date-picker-wrapper {
    display: flex;
    flex-direction: column;
    gap: 1em;
  }

  .current-range {
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-radius: 8px;
    padding: 1em;
    display: flex;
    flex-direction: column;
    gap: 0.5em;
  }
  .range-label {
    font-weight: 600;
    color: #1e293b;
    font-size: 0.9rem;
  }

  .range-dates {
    font-weight: 600;
    color: var(--blue);
  }

  .date-picker {
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  .graphs-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    gap: 5em;
    margin-right: 1em;
  }

  .graph-desc {
    margin: 0.75em 0;
    text-align: center;
  }

  .graph {
    margin-left: auto;
    margin-right: auto;
    text-align: center;
    width: 90%;
    height: 350px;
  }

  .graph-bar {
    margin-bottom: 6em;
    margin-left: auto;
    margin-right: auto;
    text-align: center;
    width: 90%;
    height: 350px;
  }

  .graph-pie {
    margin-bottom: 3em;
    margin-left: auto;
    margin-right: auto;
    text-align: center;
    width: 90%;
  }

  .graph-pie-total {
    width: 60%;
    margin-top: 2em;
  }

  .graph-charts {
    display: flex;
    flex-wrap: wrap;
    gap: 1em;
    justify-content: center;
  }

  .graph-chart {
    flex: 1;
    width: calc(50% - 1em);
  }

  .sms-statistics-section {
    display: flex;
    flex-direction: column;
    gap: 3em;
    width: 100%;
  }

  .sms-main-title {
    text-align: center;
    color: var(--blue);
    margin-bottom: 2em;
    font-size: 1.8em;
    font-weight: 600;
  }

  .sms-section-title {
    color: var(--blue);
    margin-bottom: 1em;
    font-size: 1.3em;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5em;
  }

  .sms-stat-card {
    background-color: var(--grey);
    border-radius: 12px;
    padding: 1.5em;
    margin-bottom: 2em;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border-left: 4px solid var(--blue);
  }

  .sms-stat-value {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 1em 0;
  }

  .stat-number {
    font-size: 2.5em;
    font-weight: 700;
    color: var(--blue);
    line-height: 1;
  }

  .stat-description {
    font-size: 1.1em;
    color: #666;
    margin-top: 0.5em;
  }

  .sms-stat-explanation {
    font-size: 0.95em;
    color: #555;
    line-height: 1.4;
    background-color: rgba(255, 255, 255, 0.7);
    padding: 0.8em;
    border-radius: 6px;
    margin-top: 1em;
  }

  .sms-breakdown-section {
    margin-top: 2em;
  }

  /* Enhanced Info Icon Styles */
  .info-icon {
    cursor: help;
    color: #6c757d;
    cursor: help;
    margin-left: 0.5em;
    transition: color 0.2s ease;
  }

  .info-icon:hover {
    color: var(--blue);
  }

  .remaining-sms-info {
    background-color: var(--grey);
    border-radius: 8px;
    padding: 1em;
    margin-bottom: 1em;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    text-align: center;
    width: 80%;
    margin-left: auto;
    margin-right: auto;
  }

  .monthly-sms {
    font-size: 1.1em;
  }

  /* Enhanced SMS Export Styles */
  .sms-export-section {
    margin-top: 3em;
    padding: 2em;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    border: 1px solid #dee2e6;
    border-left: 4px solid var(--green);
  }

  .export-header {
    margin-bottom: 2em;
  }

  .export-header .sms-section-title {
    display: flex;
    align-items: center;
    gap: 0.5em;
    margin-bottom: 1em;
  }

  .section-icon {
    color: var(--green);
  }

  .export-controls-grid {
    display: flex;
    flex-direction: column;
    gap: 2em;
  }

  .export-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2em;
    align-items: end;
  }

  .export-control-group {
    display: flex;
    flex-direction: column;
    gap: 0.75em;
  }

  .control-label {
    font-weight: 600;
    color: var(--dark-grey);
    font-size: 0.95em;
    margin-bottom: 0.5em;
  }

  .export-select {
    width: 100%;
  }

  .export-date-picker {
    width: 100%;
  }

  .export-actions {
    display: flex;
    gap: 1em;
    justify-content: center;
    margin-top: 1.5em;
  }

  .export-btn {
    min-width: 160px;
    height: 48px;
    font-weight: 600;
    text-transform: none;
    letter-spacing: 0.5px;
  }

  /* Enhanced Export Preview Styles */
  .export-preview {
    margin-top: 2em;
    padding: 1.5em;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 12px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
  }

  .preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5em;
    padding-bottom: 1em;
    border-bottom: 2px solid #e2e8f0;
  }

  .preview-header h4 {
    color: var(--blue);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5em;
    font-size: 1.25rem;
    font-weight: 600;
  }

  .preview-icon {
    color: var(--green);
  }

  .preview-data-table {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  }

  .timestamp-cell {
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
    color: #4a5568;
  }

  .message-content-cell {
    max-width: 300px;
  }

  .message-preview {
    display: block;
    max-width: 280px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: help;
  }

  /* Data table responsive adjustments */
  @media (max-width: 768px) {
    .export-preview {
      padding: 1em;
    }

    .preview-header {
      flex-direction: column;
      gap: 1em;
      align-items: flex-start;
    }

    .preview-header h4 {
      font-size: 1.1rem;
    }

    .message-content-cell {
      max-width: 200px;
    }

    .message-preview {
      max-width: 180px;
    }
  }

  @media screen and (max-width: 673px) {
    .sms-statistics-section {
      gap: 2em;
    }
  }

  @media (max-width: 940px) {
    .graph-charts {
      flex-direction: column;
    }

    .graph-chart {
      width: 100%;
    }
  }

  @media (min-width: 1800px) {
    .graph-pie {
      width: 50%;
    }

    .graph-pie-total {
      width: 30%;
    }
  }

  @media screen and (max-width: 673px) {
    .employee-select {
      margin-right: 1em;
    }

    .select-area-statistics {
      display: block;
    }

    .header-controls {
      grid-template-columns: 1fr;
      gap: 1.5em;
    }

    .export-row {
      grid-template-columns: 1fr;
      gap: 1.5em;
    }

    .export-actions {
      flex-direction: column;
      align-items: center;
    }

    .export-btn {
      width: 100%;
      max-width: 280px;
    }
  }

  @media screen and (max-width: 870px) {
    .graph {
      flex-direction: column;
      width: 100%;
    }

    .graph-mob {
      flex-direction: column;
      align-items: center;
      width: 90% !important;
    }

    .header-controls {
      grid-template-columns: 1fr;
      gap: 1.5em;
    }

    .export-row {
      grid-template-columns: 1fr;
      gap: 1.5em;
    }
  }

  @media screen and (max-width: 400px) {
    .graph-desc {
      font-size: 1em;
      margin-inline: 1em;
    }
  }
</style>
