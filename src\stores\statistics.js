import { axiosInstance } from '/src/code/api';
import { defineStore } from 'pinia';
import { reactive } from 'vue';
import { useAuthentication } from './authentication.js';
import { handleToastError, handleToastSuccess } from '../utility/notification.js';

export const useOverview = defineStore('graphs', () => {
  const getOverview = reactive([]);
  const getOverviewYear = reactive([]);

  const getPremiumByDate = reactive([]);
  const getFreeByDate = reactive([]);
  const getTopNoShowups = reactive([]);
  const getTopNoDelay = reactive([]);
  const getDelayAverages = reactive([]);
  const getTotalStats = reactive([]);
  const getPaymentStats = reactive([]);
  const getSmsStats = reactive([]);
  const getWaitingListPayment = reactive([]);
  const getWaitingListOverall = reactive([]);
  const range = reactive([]);
  const getPaymentRefunded = reactive([]);
  const getPaymentPaid = reactive([]);
  function getDates() {
    let today = new Date();
    let nextMonth = new Date(today.getFullYear(), today.getMonth() + 1, today.getDate());

    let formatDate = function (date) {
      return date.toISOString().slice(0, 10);
    };

    return [formatDate(today), formatDate(nextMonth)];
  }

  async function fetchOverview(from, to, id_user) {
    let dates = getDates();

    if (!from) {
      from = dates[0];
      to = dates[0];
    }

    try {
      const data = {
        from_date: from,
        to_date: to
      };
      range.splice(0, range.length);
      range.push(data);
      const response = await axiosInstance.post(`/statistics/user/${id_user}/byDateUser/`, data);
      getOverview.splice(0, getOverview.length);
      getOverview.push(response.data.data);
      if (response && response.status === 200) {
        return response;
      }
      return false;
    } catch (error) {
      const errorMessage = error?.response?.data?.message || error?.response?.data?.error || error?.message;
      handleToastError(errorMessage, 'Nastala chyba pri načítaní štatistík');
      return {
        error: error.response?.data ?? 'Unknown error'
      };
    }
  }

  async function fetchOverviewYear(year, id_user) {
    try {
      const data = {
        from_date: `${year}-01-01`,
        to_date: `${year}-12-31`
      };
      const response = await axiosInstance.post(`/statistics/user/${id_user}/byYearUser/`, data);
      getOverviewYear.splice(0, getOverviewYear.length);
      getOverviewYear.push(response.data.data);
      if (response && response.status === 200) {
        return response;
      }
      return false;
    } catch (error) {
      const errorMessage = error?.response?.data?.message || error?.response?.data?.error || error?.message;
      handleToastError(errorMessage, 'Nastala chyba pri načítaní štatistík');
      return {
        error: error.response?.data ?? 'Unknown error'
      };
    }
  }

  async function fetchPremiumByDate(from, to, id_user) {
    let dates = getDates();

    if (!from) {
      from = dates[0];
      to = dates[0];
    }

    try {
      const data = {
        from_date: from,
        to_date: to
      };
      range.splice(0, range.length);
      range.push(data);
      const response = await axiosInstance.post(`/statistics/user/${id_user}/premiumByDate/`, data);
      getPremiumByDate.splice(0, getPremiumByDate.length);
      getPremiumByDate.push(response.data.data);
      if (response && response.status === 200) {
        return response;
      }
      return false;
    } catch (error) {
      const errorMessage = error?.response?.data?.message || error?.response?.data?.error || error?.message;
      handleToastError(errorMessage, 'Nastala chyba pri načítaní štatistík');
      return {
        error: error.response?.data ?? 'Unknown error'
      };
    }
  }

  async function fetchFreeByDate(from, to, id_user) {
    let dates = getDates();

    if (!from) {
      from = dates[0];
      to = dates[0];
    }

    try {
      const data = {
        from_date: from,
        to_date: to
      };
      range.splice(0, range.length);
      range.push(data);
      const response = await axiosInstance.post(`/statistics/user/${id_user}/freeByDateUser/`, data);
      getFreeByDate.splice(0, getFreeByDate.length);
      getFreeByDate.push(response.data.data);
      if (response && response.status === 200) {
        return response;
      }
      return false;
    } catch (error) {
      const errorMessage = error?.response?.data?.message || error?.response?.data?.error || error?.message;
      handleToastError(errorMessage, 'Nastala chyba pri načítaní štatistík');
      return {
        error: error.response?.data ?? 'Unknown error'
      };
    }
  }

  async function fetchTopNoShowups(from, to, id_user) {
    let dates = getDates();

    if (!from) {
      from = dates[0];
      to = dates[0];
    }

    try {
      const data = {
        from_date: from,
        to_date: to,
        n: 10
      };
      range.splice(0, range.length);
      range.push(data);
      const response = await axiosInstance.post(`/statistics/user/${id_user}/top10NoShowupsUser/`, data);
      getTopNoShowups.splice(0, getTopNoShowups.length);
      getTopNoShowups.push(response.data.data);
      if (response && response.status === 200) {
        return response;
      }
      return false;
    } catch (error) {
      const errorMessage = error?.response?.data?.message || error?.response?.data?.error || error?.message;
      handleToastError(errorMessage, 'Nastala chyba pri načítaní štatistík');
      return {
        error: error.response?.data ?? 'Unknown error'
      };
    }
  }

  async function fetchTopNoDelay(from, to, id_user) {
    let dates = getDates();

    if (!from) {
      from = dates[0];
      to = dates[0];
    }
    try {
      const data = {
        from_date: from,
        to_date: to,
        n: 10
      };
      range.splice(0, range.length);
      range.push(data);
      const response = await axiosInstance.post(`/statistics/user/${id_user}/top10NoDelayUSer/`, data);
      getTopNoDelay.splice(0, getTopNoDelay.length);
      getTopNoDelay.push(response.data.data);
      if (response && response.status === 200) {
        return response;
      }
      return false;
    } catch (error) {
      const errorMessage = error?.response?.data?.message || error?.response?.data?.error || error?.message;
      handleToastError(errorMessage, 'Nastala chyba pri načítaní štatistík');
      return {
        error: error.response?.data ?? 'Unknown error'
      };
    }
  }

  async function fetchDelayAverages(from, to, id_user) {
    let dates = getDates();

    if (!from) {
      from = dates[0];
      to = dates[0];
    }
    try {
      const data = {
        from_date: from,
        to_date: to
      };
      range.splice(0, range.length);
      range.push(data);

      const response = await axiosInstance.post(`/statistics/user/${id_user}/delayAveragesUser/`, data);
      getDelayAverages.splice(0, getDelayAverages.length);
      getDelayAverages.push(response.data.data);
      if (response && response.status === 200) {
        return response;
      }
      return false;
    } catch (error) {
      const errorMessage = error?.response?.data?.message || error?.response?.data?.error || error?.message;
      handleToastError(errorMessage, 'Nastala chyba pri načítaní štatistík');
      return {
        error: error.response?.data ?? 'Unknown error'
      };
    }
  }

  async function fetchTotalStats(from, to, id_user) {
    let dates = getDates();

    if (!from) {
      from = dates[0];
      to = dates[0];
    }

    try {
      const data = {
        from_date: from,
        to_date: to
      };
      range.splice(0, range.length);
      range.push(data);
      const response = await axiosInstance.post(`/statistics/user/${id_user}/totalStatisticsUser/`, data);
      getTotalStats.splice(0, getTotalStats.length);
      getTotalStats.push(response.data.data);
      if (response && response.status === 200) {
        return response;
      }
      return false;
    } catch (error) {
      const errorMessage = error?.response?.data?.message || error?.response?.data?.error || error?.message;
      handleToastError(errorMessage, 'Nastala chyba pri načítaní štatistík');
      return {
        error: error.response?.data ?? 'Unknown error'
      };
    }
  }
  async function fetchPayment(from, to, id_user) {
    let dates = getDates();

    if (!from) {
      from = dates[0];
      to = dates[0];
    }

    try {
      const data = {
        from_date: from,
        to_date: to
      };
      range.splice(0, range.length);
      range.push(data);
      const response = await axiosInstance.post(`/statistics/user/${id_user}/paymentStatisticsCountsDateRange/`, data);
      getPaymentStats.splice(0, getPaymentStats.length);
      getPaymentStats.push(response.data.data);
      if (response && response.status === 200) {
        return response;
      }
      return false;
    } catch (error) {
      const errorMessage = error?.response?.data?.message || error?.response?.data?.error || error?.message;
      handleToastError(errorMessage, 'Nastala chyba pri načítaní štatistík');
      return {
        error: error.response?.data ?? 'Unknown error'
      };
    }
  }

  async function fetchPaymentPaid(from, to, id_user) {
    let dates = getDates();

    if (!from) {
      from = dates[0];
      to = dates[0];
    }

    try {
      const data = {
        from_date: from,
        to_date: to
      };
      range.splice(0, range.length);
      range.push(data);
      const response = await axiosInstance.post(`/statistics/user/${id_user}/paymentsPayedOverviewDate/`, data);
      getPaymentPaid.splice(0, getPaymentPaid.length);
      getPaymentPaid.push(response.data.data);
      if (response && response.status === 200) {
        return response;
      }
      return false;
    } catch (error) {
      const errorMessage = error?.response?.data?.message || error?.response?.data?.error || error?.message;
      handleToastError(errorMessage, 'Nastala chyba pri načítaní štatistík');
      return {
        error: error.response?.data ?? 'Unknown error'
      };
    }
  }
  async function fetchPaymentRefunded(from, to, id_user) {
    let dates = getDates();

    if (!from) {
      from = dates[0];
      to = dates[0];
    }

    try {
      const data = {
        from_date: from,
        to_date: to
      };
      range.splice(0, range.length);
      range.push(data);
      const response = await axiosInstance.post(`/statistics/user/${id_user}/paymentsRefundedOverviewDate/`, data);
      getPaymentRefunded.splice(0, getPaymentRefunded.length);
      getPaymentRefunded.push(response.data.data);
      if (response && response.status === 200) {
        return response;
      }
      return false;
    } catch (error) {
      const errorMessage = error?.response?.data?.message || error?.response?.data?.error || error?.message;
      handleToastError(errorMessage, 'Nastala chyba pri načítaní štatistík');
      return {
        error: error.response?.data ?? 'Unknown error'
      };
    }
  }

  async function fetchSms(from, to, id_user) {
    let dates = getDates();

    if (!from) {
      from = dates[0];
      to = dates[0];
    }

    try {
      const data = {
        from_date: from,
        to_date: to
      };
      range.splice(0, range.length);
      range.push(data);
      const response = await axiosInstance.post(`/statistics/user/${id_user}/smsStatisticsDetailed/`, data);
      getSmsStats.splice(0, getSmsStats.length);
      getSmsStats.push(response.data.data);
      if (response && response.status === 200) {
        return response;
      }
      return false;
    } catch (error) {
      const errorMessage = error?.response?.data?.message || error?.response?.data?.error || error?.message;
      handleToastError(errorMessage, 'Nastala chyba pri načítaní štatistík');
      return {
        error: error.response?.data ?? 'Unknown error'
      };
    }
  }

  async function fetchWaitingListPurchase(from, to, id_user) {
    let dates = getDates();

    if (!from) {
      from = dates[0];
      to = dates[0];
    }

    try {
      const data = {
        from_date: from,
        to_date: to
      };
      range.splice(0, range.length);
      range.push(data);
      const response = await axiosInstance.post(`/statistics/user/${id_user}/waitingListStatistics/payment/`, data);
      getWaitingListPayment.splice(0, getWaitingListPayment.length);
      getWaitingListPayment.push(response.data.data);
      if (response && response.status === 200) {
        return response;
      }
      return false;
    } catch (error) {
      const errorMessage = error?.response?.data?.message || error?.response?.data?.error || error?.message;
      handleToastError(errorMessage, 'Nastala chyba pri načítaní štatistík');
      return {
        error: error.response?.data ?? 'Unknown error'
      };
    }
  }
  async function fetchWaitingListOverall(from, to, id_user) {
    let dates = getDates();

    if (!from) {
      from = dates[0];
      to = dates[0];
    }

    try {
      const data = {
        from_date: from,
        to_date: to
      };
      range.splice(0, range.length);
      range.push(data);
      const response = await axiosInstance.post(`/statistics/user/${id_user}/waitingListStatistics/`, data);
      getWaitingListOverall.splice(0, getWaitingListOverall.length);
      getWaitingListOverall.push(response.data.data);
      if (response && response.status === 200) {
        return response;
      }
      return false;
    } catch (error) {
      const errorMessage = error?.response?.data?.message || error?.response?.data?.error || error?.message;
      handleToastError(errorMessage, 'Nastala chyba pri načítaní štatistík');
      return {
        error: error.response?.data ?? 'Unknown error'
      };
    }
  }

  return {
    getOverview,
    fetchOverview,
    getPremiumByDate,
    getFreeByDate,
    getTopNoShowups,
    getTopNoDelay,
    getDelayAverages,
    getTotalStats,
    getPaymentStats,
    getSmsStats,
    getPaymentRefunded,
    fetchPaymentPaid,
    getPaymentPaid,
    getWaitingListOverall,
    fetchWaitingListOverall,
    range,
    fetchPaymentRefunded,
    fetchOverviewYear,
    getOverviewYear,
    fetchPremiumByDate,
    fetchFreeByDate,
    fetchTopNoShowups,
    fetchTopNoDelay,
    fetchDelayAverages,
    fetchTotalStats,
    fetchPayment,
    fetchSms,
    getWaitingListPayment,
    fetchWaitingListPurchase
  };
});
